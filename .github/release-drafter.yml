# Configuration for Release Drafter: https://github.com/toolmantim/release-drafter
name-template: 'v$NEXT_PATCH_VERSION 🌈'
tag-template: 'v$NEXT_PATCH_VERSION'
version-template: $MAJOR.$MINOR.$PATCH
# Emoji reference: https://gitmoji.carloscuesta.me/
categories:
  - title: '🚀 Features'
    labels:
      - 'feature'
      - 'enhancement'
      - 'kind/feature'
  - title: '🐛 Bug Fixes'
    labels:
      - 'fix'
      - 'bugfix'
      - 'bug'
      - 'regression'
      - 'kind/bug'
  - title: 📝 Documentation updates
    labels:
      - documentation
      - 'kind/doc'
  - title: 👻 Maintenance
    labels:
      - chore
      - dependencies
      - 'kind/chore'
      - 'kind/dep'
  - title: 🚦 Tests
    labels:
      - test
      - tests
exclude-labels:
  - reverted
  - no-changelog
  - skip-changelog
  - invalid
change-template: '* $TITLE (#$NUMBER) @$AUTHOR'
template: |
  ## What’s Changed
  $CHANGES