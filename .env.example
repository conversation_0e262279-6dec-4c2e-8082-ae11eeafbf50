# 网络设置
PORT=3618

# Kubernetes 配置
KUBECONFIG=~/.kube/config

# 大模型 API 配置
OPENAI_API_KEY="" # 请替换为实际的 API 密钥
OPENAI_API_URL="" # 请替换为实际的 API 地址
OPENAI_MODEL="" # 请替换为实际的模型名称

#InCluster模式
IN_CLUSTER=true

# 登录与认证
LOGIN_TYPE=password

# 调试与日志s
DEBUG=false
LOG_V=2

# JWT Token 密钥
JWT_TOKEN_SECRET=your-secret-key

# Shell 镜像地址
KUBECTL_SHELL_IMAGE=bitnami/kubectl:latest
NODE_SHELL_IMAGE=alpine:latest



# 数据库类型（sqlite、mysql、postgresql等）
DB_DRIVER=postgresql
# SQLite数据库路径（仅DB_DRIVER=sqlite时生效）
SQLITE_PATH=./data/k8m.db


# MySQL数据库配置（仅DB_DRIVER=mysql时生效）
MYSQL_HOST=127.0.0.1
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=root
MYSQL_DATABASE=k8m
MYSQL_CHARSET=utf8mb4
MYSQL_COLLATION=utf8mb4_general_ci
MYSQL_QUERY=parseTime=True&loc=Local
MYSQL_LOGMODE=false

# PostgreSQL数据库配置（仅DB_DRIVER=postgresql时生效）
PG_HOST=127.0.0.1
PG_PORT=5432
PG_USER=root
PG_PASSWORD=root
PG_DATABASE=k8m
PG_SSLMODE=disable
PG_TIMEZONE=Asia/Shanghai
PG_LOGMODE=false

# LDAP配置
LDAP_ENABLED=false
LDAP_HOST=
LDAP_PORT=389
LDAP_USERNAME=
LDAP_PASSWORD=
LDAP_BASEDN=
LDAP_BINDUSERDN=
LDAP_ANONYMOUSQUERY=0
LDAP_USERFIELD=sAMAccountName
LDAP_LOGIN2AUTHCLOSE=true
