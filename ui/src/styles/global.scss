/* 在你的 CSS 文件中添加以下代码 */
html,
body {
  overscroll-behavior-y: contain;
  /* 防止滚动溢出时的反弹效果 */
  height: 100%;
  /* 设置高度为 100% 以覆盖整个视口 */
  overflow: hidden;
  /* 隐藏溢出内容 */
}

.app-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}
/* 如果你的内容需要滚动，可以为特定容器添加滚动样式 */
.scrollable-container {
  height: 100%;
  overflow-y: auto;
  /* 允许垂直滚动 */
}
// *::-webkit-scrollbar {
//   width: 0px;
// }
// *::-webkit-scrollbar-thumb {
//   border-radius: 10px;
//   background: rgba(0, 0, 0, 0.2);
// }
// *::-webkit-scrollbar-track {
//   border-radius: 0;
//   background: rgba(0, 0, 0, 0.1);
// }

.cxd-Page-body {
  overflow: auto;
}

.pointer {
  cursor: pointer;
}


        .floating-toolbar {
            position: fixed;
            left: 160px;
            /* 距离左边 */
            top: 0;
            /* 距离顶部 */
            z-index: 1000;
            /* 确保显示在上层 */
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(255, 255, 255, 0);
            padding: 10px;
            height: 60px;
        }

        .floating-toolbar-right {
            position: fixed;
            right: 60px;
            /* 距离右边 */
            top: 0;
            /* 距离顶部 */
            z-index: 1000;
            /* 确保显示在上层 */
            /*display: flex;*/
            align-items: center;
            justify-content: center;
            background-color: rgba(255, 255, 255, 0);
            /*padding: 10px;*/
            height: 85px;
        }

/*隐藏select上选项的鼠标浮动图层*/
.cxd-Tooltip.cxd-Tooltip--top.cxd-Tooltip--light {
  display: none
}


.cxd-Select .cxd-PopOver.cxd-Select-popover.cxd-PopOver--v-top {
  width: fit-content !important;
}


.cxd-Page-asideResizor {
  z-index: 9900 !important;
}

.cxd-Tree-item .is-checked .cxd-Tree-itemLabel-item {
  background-color: #d7e7fb !important;
}
/*让下拉菜单的图标好看一些*/
.cxd-DropDown-menu .cxd-Button-icon {
  margin-right: 10px;
}

.amis-scope .cxd-Link i {
  font-size: var(--link-icon-size);
  margin-left: 0;
  margin-right: 8px;
}
//调整下antd select 的下拉框层索引
// amis 弹窗的索引是1400
.ant-select-dropdown {
  z-index: 1401 !important;
}


.ant-picker-dropdown {
  z-index: 1401 !important;
}
.ant-tooltip{
    z-index: 1401 !important;
}
.ant-modal-wrap{
    z-index: 180000 !important;
}