const enTranslation = `
1. API密钥用于程序化访问平台，可用于自动化脚本或第三方工具集成。=1. API Key is for programmatic access, automation scripts, or 3rd-party tools.
2. 密钥具有与当前用户相同的权限。=2. Key has same permissions as current user.
2FA状态=MFA status
2步验证=MFA
AI功能开关=EnableAI
AI总结=AI Summary
AI模型配置=Model
AI配置=AI Conf
AI问诊=AI Consultation
API 服务=API
APIService=APIService
API密钥=API Key
API密钥使用说明：=API Key Usage Guide:
CPU(m毫核)/内存(Mi)=CPU (millicore)/Memory (Mi)
CPU设置=CPU Setting
CPU请求/限制：未设置/100m=CPU Request/Limit: Not set/100m
CPU请求=CPU Request
CPU资源=CPU Resource
CPU限制=CPU Limit
cpu（核）=CPU (Core)
Cron表达式=Cron Expression
CSI节点=CSI Node
Exec权限：=Exec Permission:
Git仓库=Git Repository
Git提交=Git Commit
Git标签=Git Tag
go版本=Go Version
http://IP地址:端口/auth/oidc/auth0/callback=http://IP:Port/auth/oidc/auth0/callback
HTTP头信息=HTTP Header
HTTP请求=HTTP Request
HTTP请求设置=HTTP Request Setting
HTTP路径=HTTP Path
Ingress入口=Ingress
Ingress入口类=IngressClass
Ingress数量=IngressCount
IP协议栈=IPStack
IP协议栈集=IPStacks
Kubectl Shell 镜像。默认为 bitnami/kubectl:latest，必须包含kubectl命令=KubectlShell Image (default: bitnami/kubectl:latest, must include kubectl)
Kubectl Shell镜像=Kubectl shell image
MCP导航=MCP Navigation
MCP执行记录=MCP Log
MCP服务器工具信息=MCP Server Tool Info - k8m
MCP管理=MCP Mgm
NodeShell 镜像。默认为 alpine:latest，必须包含nsenter命令=NodeShell Image (default: alpine:latest, must include nsenter)
NodeShell 镜像。默认为 alpine:latest，必须包含nsenter命令=NodeShell image. The default is alpine:latest, which must include the nsenter command
Pod中断配置=PDB
Pod副本的数量=Replicas
POD选择器=Selector
PVC声明=PVC
SSO配置管理=SSO Config
SSO（单点登录）配置用于设置外部认证服务，支持OIDC协议。启用后，用户可以通过外部认证服务登录系统。=SSO config for external auth (OIDC). After enabled, users can login via external auth.
SVC服务=SVC
TCP端口=TCP Port
TCP端口检查=TCP Port
TCP端口检查设置=TCP Port
webhook管理=Webhook
Yaml管理=YAML Management
Yaml编辑=YAML Edit
上下文记忆=Context
上限用量占比=Limit Percentage
不存在时拉取=IfNotPresent
不设置=Not Set
专项巡检=Special Inspection
为Kubernetes资源添加或删除注解 / Add or remove annotations for Kubernetes resource=Add/Remove K8s resource annotation
主体=Subject
事件=Event
事件来源=Source
产品名称=Product name
亲和性=Affinity
仅可查看集群资源信息，无法进行修改操作。=You can view only the cluster resource information and cannot modify it.
从不拉取=Never
仓库URL=Repository URL
以日志级别为例，启动参数设置2，环境变量设置3，界面配置4，那么最终生效日志级别为4=Log Level Example: Startup Param=2, Env=3, UI=4, Final=4
任务=Job
任意选择=AnySelect
优先级名称=Priority Name
优先级类=PriorityClass
优先级配置=Priority Configuration
会话亲和性=Affinity
使用方法=How to
供应者=Provider
保 存=Save
保留策略=Retention Policy
保障等级=QoS
修改密码=Change Password
修改密码=Change your password
值=Value
停止=Stop
健康检查端口号=Health Check Port
健康检查路径，如：/healthz=Health Check Path, e.g. /healthz
健康检查配置=Health
允许数字、字母和'-'组成，不能以'-'和数字开头，不能以'-'结尾=Allowed: numbers, letters, and '-', cannot start with '-' or a number, cannot end with '-'
入口控制器=IngressController
全局模式=Global
全选=All
全部集群=All Clusters
共=Total
关于=About
关联字段=Associated
关闭=Close
具有进入容器内，执行命令的权限=Has permission to exec in container
内存设置=Memory Setting
内存请求/限制：未设置/64Mi=Memory Request/Limit: Not set/64Mi
内存请求=Memory Request
内存资源=Memory Resource
内存限制=Memory Limit
内存（Mi）=Memory (Mi)
内置参数=Built-in Parameter
内部流量策略=Internal Traffic Policy
分组=Group
创建=Create
创建API密钥=Create API Key
创建密钥=Create Key
创建时间=Create Time
创建访问密钥=Create Access Key
初始延迟秒数=Initial Delay Seconds
删除=Delete
删除Pod中的指定文件 (类似命令: kubectl exec <pod-name> -n <namespace> -c <container> -- rm <path>) / Delete file in pod=Delete file in pod (similar command: kubectl exec <pod-name> -n <namespace> -c <container> -- rm) <path>/ Delete file in pod
刷新=Refresh
副本=Replica
副本数范围=Replicas
副本数量=Replicas
副本集=ReplicaSet
加载中...=Loading...
匹配优先级=Match Priority
区分器类型=Differentiator Type
区间=Interval
协议=Protocol
单栈模式=SingleStack
单点登录=SSO
单路读写=ReadWriteOnce
卷模式=Volume Mode
卷绑定模式=Mode
原因=Reason
参数=Param
参数查看（ESC 关闭）=View Param (ESC Off)
参数设置=Params
双栈优先=DualStackPreferred
发起人=Creator
取消=Cancel
变更钩子=MutatingWebhookConfiguration
可以为用户、用户组分别授权，当为用户组时，对组内所有用户生效操作。=You can grant permissions to users and user groups separately, and if they are user groups, the actions take effect on all users in the group.
可以管理和操作所有集群资源，包括创建、修改、删除、Exec等操作。=You can manage and operate all cluster resources, including create, modify, delete, and Exec.
可访问性=Accessibility
可选的自定义HTTP头信息=Optional Custom HTTP Header
名称=Name
启动时间=Started
启用=Enable
告警=Alert
命名空间=Namespace
回收策略=Reclaim
回调地址=Callback
回调定义=Callback
在HTTP请求头中添加Header：=Add Header to HTTP Request
在Kubernetes资源的监控状态中，若指标类型（如DiskPressure、Unavailable等）的Status值为False，则代表该指标处于正常健康状态。例如：DiskPressure=False表示磁盘无压力，Unavailable=False表示服务可用。=If a K8s resource metric (e.g. DiskPressure, Unavailable) Status is False, it is healthy. E.g. DiskPressure=False means no disk pressure, Unavailable=False means service available.
在Kubernetes资源的监控状态中，若指标类型（如DiskPressure、Unavailable等）的Status值为False，则代表该指标处于正常健康状态。例如：DiskPressure=False表示磁盘无压力，Unavailable=False表示服务可用。=If the Status value of a metric type (such as DiskPressure or Unavailable) is False in the monitoring status of a Kubernetes resource, the metric is in a healthy state. For example, DiskPressure=False indicates that the disk is under no pressure, and Unavailable=False indicates that the service is available.
域名=DomainName
外部地址=External Address
多集群管理=MultiClusterManagement
失败阈值=Failure Threshold
如: 1 或 1000m=E.g.: 1 or 1000m
如: 1Gi 或 2Gi=E.g.: 1Gi or 2Gi
如: 500m 或 0.5=E.g.: 500m or 0.5
如: 512Mi 或 1Gi=E.g.: 512Mi or 1Gi
始终拉取=Always
存储=Storage
存储卷声明数量=PVC Count
存储卷数量=PV Count
存储容积=Storage Size
存储类=StorageClass
存储类名称=StorageClass Name
存在时长=Duration
存活探针=Liveness Probe
守护进程集=DaemonSet
定时任务=CronJob
实例数=Instances
实时用量=Real-time Usage
实时用量占比=Real-time Percentage
容器=Container
容器列表=Containers
容器名称=>镜像:版本=Container => Image:Tag
容器启动后等待多少秒开始探测=Seconds to Wait After Container Start
容器启动时请求的CPU资源=Initial CPU Request
容器启动时请求的内存资源=Initial Memory Request
容器最大可使用的CPU资源=Max CPU Limit
容器最大可使用的内存资源=Max Memory Limit
容器组=Pod
容器组IP=Pod IP
容器组资源用量（ESC 关闭）=Pod Resource Usage (ESC Off)
容器网络=Network
容器设置=Container
容器镜像=Container Image
容器：=Container:
容忍度=Toleration
密钥ID=Key ID
密钥值=Key Value
就绪=Ready
就绪IP=Ready IP
就绪探针=Readiness Probe
就绪检查端口号=Readiness Port
就绪检查路径，如：/ready=Readiness Path, e.g. /ready
尽力型=Best Effort
展开=Expand
属性文档=Doc
巡检汇总=Summary
巡检规则=Rules
巡检计划=Plan
巡检记录=Records
工具=Tool
工具名称=Tool Name
已完成=Completed
已绑定=Bound
已连接=Connected
平台=Platform
平台管理员=Platform Admin
平台管理员组=Platform Admin Group
平台设置=Platform Config
并发权重=Concurrency Weight
并发策略=Concurrency Policy
应用规则：如果一个资源状态的类型，包含下表中的指标名称，则会翻转显示。=Rule: If a resource status type contains a metric name below, it is flipped.
延迟绑定=Delayed Binding
建议填写version: v1,如不需要可以删除=Recommended: version: v1, remove if not needed
开启=Enable
开始时间=Started
开放MCP=Open MCP
开放MCP服务=Open MCP
强制删除=Force Delete
当前/预期=Current/Desired
当前容器资源=Current Container Resource
当前状态=Current
当前运行镜像=Current Image
思考链=Chain of Thought
总执行次数：=Total Executions:
总集群数：=Total Clusters:
恢复=Resume
成功阈值=Success Threshold
我已经获得集群授权情况。权限解释：=Cluster authorization obtained. Permission explanation:
我的集群=My Cluster
所在节点=Node
所有者=Owner
执行命令=Execute Command
执行命令设置=Command
执行时间=Exec Time
执行结果=Exec Result
执行耗时=Exec Duration
扩缩容=Scale
批量删除=Bulk Delete
抢占=Preempt
抢占策略=Preemption Policy
持久卷=PV
持久卷声明=PVC
持续秒数=Duration (Seconds)
指南=Guide
指标=Metric
指标名称 / 阈值 / 当前值=Metric/Threshold/Current
指标名称=Metric Name
指标显示翻转=MetricFlip
授权=Authorization
授权人=Authorizer
授权时间=Authorization Time
授权类型=Authorization Type
授权类型：=Authorization Type:
探测超时时间（秒）=Probe Timeout (Seconds)
探针类型=Probe Type
控制器名称=ControllerName
描述=Desc
描述Pod容器组，(类似命令: kubectl describe pod -n <namespace> pod_name ) =Describe Pod (kubectl describe pod -n <namespace> <pod_name>)
描述信息=Desc
提交=Submit
搜索=Search
操作=Operate
收起=Collapse
数据库=Database
文件=File
文件系统=FileSystem
文档=Doc
断开=Disconnect
新增 Deployment 参数填写=Add Deployment Parameters
新增=Add
新增cm=Add
新增Deployment=Add
新增svc=Add
新建Webhook=Add
新建巡检计划=Add
新建指标  (ESC 关闭)=New Indicator (ESC Off)
新建用户  (ESC 关闭)=Add User (ESC Off)
新建用户=Add
新建用户组=Add
新建规则=Add
新建配置=Add
方式一: 动态地址=Method 1: Dynamic Address
方式二：静态地址+Header=Method 2: Static Address + Header
无=None
是否使用k8m内置AI大模型=Use Built-in k8m AI Model
是否启用=Enabled
是否启用AI功能，默认开启=Enable AI (Default)
是否开启任意选择，默认开启=Enable Any Option (Default)
是否默认=Default
显示设置=Display
普通=Normal
普通用户=Normal User
智检=AI Inspection
暂停=Pause
暂无数据=No Data
暂无集群数据=No Cluster Data
更新索引=Updated
更新资源限制=Update Resource Limit
更新镜像=Update Image
最后调度=Last Schedule
最后运行=Last Run
最大倍率=Max Ratio
有状态集=StatefulSet
服务器名称=Server Name
服务器地址=Server Address
服务账户=ServiceAccount
未启用=Not Enabled
未就绪=Not Ready
未连接=Not Connected
权限=Permission
条件=Condition
来源=Source
查 询=Query
查=Query
查看事件   (ESC 关闭)=View Events (ESC Off)
查看事件=View Event
查看使用说明=View Usage Guide
查看编辑=View/Edit
查询Deployment的HPA列表。对应kubectl命令: kubectl get hpa -n <namespace> | grep <deployment-name> / Query deployment HPA list. Equivalent kubectl command: kubectl get hpa -n <namespace> | grep <deployment-name>=Get HPA for Deployment (kubectl get hpa -n <namespace> | grep <deployment-name>)
标签=Label
标签设置=Label
模型名称=Model Name
模型名称=The name of the model
模糊搜索label标签中的k、v=Fuzzy Search k/v in Label
模糊标签=Fuzzy Label
每次探测间隔多少秒=Probe Interval Seconds
每页显示=PerPage
水平自动扩缩=HPA
汇总数据：=Summary Data:
注解=Annotation
流量规则=Traffic Policy
消息模板=Template
添加参数=Add
添加容器=Add
添加服务器=Add
清空节点上的Pod并防止新的Pod调度，等同于kubectl drain <node> / Drain all pods from node and prevent new scheduling, equivalent to kubectl drain <node>=Drain Node (kubectl drain <node>)
温馨提示：MCP服务器启用后，在大模型对话中，会自动注册使用。=Tip: After MCP server is enabled, it will auto-register in LLM dialog.
滚动更新=Rolling Update
点击查看=View
点击选择显示列=Columns
版本=Version
版本号=Version
版本标签=Version Label
状态=Status
状态指标管理=Status Metric Management
生效范围=Effective Scope
用户名=Username
用户管理=User Management
用户组=User Group
用户组管理=User Group Management
申请容量=Capacity
界面展示实时用量、指标、Pod元数据等资源的缓存时间（单位：秒），默认60秒。时间越短，界面变化越快，但是会增加k8s系统负担。=The page displays the cache time (unit: seconds) of resources such as real-time usage, metrics, and pod metadata, which is 60 seconds by default. The shorter the time, the faster the interface changes, but it will increase the burden on the K8S system.
登录设置=Login Setting
白名单命名空间=Whitelist Namespace
目标=Target
目标URL=Target URL
目标集群=Target Cluster
确认=Confirm
示例配置（JSON）=Example(JSON)
禁用=Disable
租约=Lease
端口=Port
端点=Endpoint
端点切片=EndpointSlice
策略=Policy
签名校验=Signature
类型=Type
精确标签=Precise Label
系统规则=System Rule
索引时间=Indexed
纳管集群=Add
组件状态=ComponentStatus
组名=Group Name
绑定=Bind
结束时间=Ended
编译器=Compiler
编译时间=Build Time
编辑=Edit
编辑资源限制=Edit Resource Limit
网络策略=NetworkPolicy
网络类型=Type
置空表示不限制，可访问该集群下所有的命名空间。如果填写了，那么用户就只能访问指定的命名空间了。=Leave blank for all namespaces. If filled, user can only access specified namespaces.
置空表示不限制，如果填写了，那么用户将不能访问该命名空间。黑名单可否定白名单。黑名单权限最高。=Leave blank for no limit. If filled, user cannot access this namespace. Blacklist overrides whitelist. Blacklist has highest permission.
自动对话轮数=Auto Conversation Rounds
节点=Node
节点Shell镜像=NodeShell Image
规则=Rules
规则名称=Rule Name
规则描述=Rule Description
规则数=Rule Count
规则编码=Rule Code
角色=Role
角色绑定=RoleBinding
解除纳管=Remove
触发类型=TriggerType
计划ID=PlanID
计划名称=Plan Name
认证服务器=Auth Server
设为默认=Set Default
设定副本数=Replicas
设置产品显示名称=Set Product Display Name
设置查询字段=Query
设置环境变量=Environment
设置节点为不可调度状态，等同于kubectl cordon <node> / Mark node as unschedulable, equivalent to kubectl cordon <node>=<node> Mark node as unschedulable, equivalent to kubectl cordon <node>
访问地址=Access Address
访问方式=Access
访问模式=Mode
访问端口=Access Port
访问端口[:节点端口]/协议 => 容器端口=AccessPort[:NodePort]/Protocol=>ContainerPort
访问类型=Access Type
访问规则=AccessRule
证书有效期=CertificateExpiry
详情=Detail
说明=Desc
请将下面的地址，填入认证服务器的回调地址中。=Fill the following address in the auth server callback.
请求<设定<上限=Request<Setting<Limit
请求用量占比=Request Percentage
请求限制=Request
请注意替换为可访问的IP（域名）、端口=Replace with accessible IP/domain/port
请输入命令=Enter Command
请输入密钥用途描述=Enter Key Purpose
请输入指标名称，包含即为命中。如Pressure、Unavailable等=Enter Metric Name (e.g. Pressure, Unavailable)
请输入指标的描述信息=Enter Metric Description
请输入用户名=Please Enter Username
请输入访问链接用途描述=Enter Purpose of Access Link
请选择=Select
请选择日期=Select Date
请选择角色=Select Role
调度中=Scheduling
调度计划=Schedule
调整副本数量=Replicas
调整资源限制=ResourceLimit
资源描述=Desc
资源用量=Usage
资源缓存时间=Resource Cache Time
资源设定=Setting
资源配额=ResourceQuota
起始时间=Start Time
超时秒数=Timeout Seconds
转换次数=Conversion Count
输入PVC声明名称=Enter PVC Name
输入主体名称=Enter Subject Name
输入仓库名称=Enter Repository Name
输入名称=Enter Name
输入命名空间=Enter Namespace
输入指标名称=Enter Metric Name
输入描述=Enter Description
输入用户名=Enter Username
输入角色名称=Enter Role Name
输入集群名称=Enter a name for the cluster
运行中=Running
运行时类=RuntimeClass
进度=Progress
进行中=In Progress
连接=Connect
连续失败多少次标记为不健康=Unhealthy After Consecutive Failures
连续成功多少次标记为健康=Healthy After Consecutive Successes
适当提升有助于提升回答完整度=Appropriate boosting can help improve the completeness of your answers
适当的记忆量有助于提升回答精度=The right amount of memory can help improve the accuracy of your answers
选取数量=Pick Count
选择deployment部署的命名空间=Select Deployment Namespace
选择保障等级=QoS
选择状态=Status
通用参数设置=General Parameter Setting
通过YAML创建或更新Kubernetes资源，等同于 'kubectl apply -f <yaml-file>' / Apply Kubernetes resources from YAML, equivalent to 'kubectl apply -f <yaml-file>'=Apply K8s resource by YAML (kubectl apply -f <yaml-file>)
通过YAML删除Kubernetes资源，等同于 'kubectl delete -f <yaml-file>' / Delete Kubernetes resources from YAML, equivalent to 'kubectl delete -f <yaml-file>'=Delete K8s resource by YAML (kubectl delete -f <yaml-file>)
通过集群、命名空间和名称删除Kubernetes资源 / Delete Kubernetes resource by cluster, namespace, and name=Delete K8s resource by cluster/namespace/name
通过集群、命名空间和名称获取Kubernetes资源详情 / Retrieve Kubernetes resource details by cluster, namespace, and name=Get K8s resource by cluster/namespace/name
部署=Deploy
配置=Config
配置加载顺序 启动参数->环境变量->数据库参数设置（界面配置）=Config Load Order: Startup Param -> Env Var -> DB Param (UI)
配置名称=Config Name
配置映射=ConfigMap
配置管理=Config Management
配额范围=Quota Scope
重启=Restart
重启次数=Restart
重新扫描=Rescan
重置=Reset
重置内置规则=Reset Built-in Rules
错误=Error
错误信息=Error Message
错误数量=Errors
镜像=Image
镜像名称=Image Name
镜像拉取密钥=Image Pull Secret
镜像拉取策略=Image Pull Policy
镜像拉取超时时间=Image Pull Timeout
队列上限=Queue cap
队列数量=Queue Count
限制范围=Limit
集群=Cluster
集群只读：=Cluster Read-Only:
集群名称=Cluster Name
集群地址=ClusterAddress
集群巡检设置=Inspection
集群权限=Cluster Permission
集群版本=Cluster version
集群管理员：=Cluster Admin:
集群角色=ClusterRole
集群角色绑定=ClusterRoleBinding
集群配置=Cluster Config
需要检查的TCP端口号=TCP Port
驱动=Driver
验证钩子=ValidatingWebhookConfiguration
黑名单命名空间=Blacklist Namespace
黑名单命名空间：=Blacklist Namespace:
默认值=Default
默认模型=Default Model
默认请求值=Default Value
`;

export default enTranslation;
