{"type": "page", "body": [{"type": "container", "className": "floating-toolbar-right", "body": [{"type": "wrapper", "style": {"display": "inline-flex"}, "body": [{"type": "form", "mode": "inline", "wrapWithPanel": false, "body": [{"label": "集群", "type": "select", "multiple": false, "name": "cluster", "id": "cluster", "searchable": true, "source": "/params/cluster/option_list", "value": "${ls:cluster}", "onEvent": {"change": {"actions": [{"actionType": "custom", "script": "localStorage.setItem('cluster', event.data.value)"}, {"actionType": "custom", "script": "window.location.reload();"}]}}}]}]}]}, {"type": "crud", "id": "detailCRUD", "name": "detailCRUD", "autoFillHeight": true, "autoGenerateFilter": {"columnsNum": 4, "showBtnToolbar": false}, "headerToolbar": [{"type": "columns-toggler", "align": "right", "draggable": true, "icon": "fas fa-cog", "overlay": true, "footerBtnSize": "sm"}, {"type": "tpl", "tpl": "共${count}条", "align": "right", "visibleOn": "${count}"}, "reload", "bulkActions"], "bulkActions": [], "loadDataOnce": false, "syncLocation": false, "initFetch": true, "perPage": 10, "footerToolbar": [{"type": "pagination", "align": "right"}, {"type": "statistics", "align": "right"}, {"type": "switch-per-page", "align": "right"}], "api": "get:/mgm/helm/chart/list", "columns": [{"type": "operation", "label": "操作", "width": 120, "buttons": [{"type": "button", "label": "详情", "actionType": "drawer", "drawer": {"title": "${name} 详情 （ESC 关闭）", "size": "lg", "closeOnEsc": true, "body": {"type": "form", "mode": "horizontal", "horizontal": {"left": 2, "right": 8}, "controls": [{"type": "static-image", "name": "icon", "thumbMode": "contain", "thumbRatio": "1:1", "width": "100px", "height": "100px"}, {"type": "static", "name": "name", "label": "Chart名称"}, {"type": "hidden", "name": "repository_name", "label": "仓库名称"}, {"type": "select", "name": "install_version", "label": "版本", "clearable": true, "value": "${latest_version}", "source": "/mgm/helm/repo/${repository_name}/chart/${name}/versions"}, {"type": "static", "name": "description", "label": "描述"}, {"label": "项目主页", "type": "tpl", "tpl": "<a href=${home} target=_blank>主页</a>&nbsp;&nbsp;&nbsp;&nbsp;<a href=${sources} target=_blank>源码</a>"}, {"type": "static", "name": "keywords", "label": "关键词", "tpl": "${keywords | join: ', '}"}, {"type": "static", "name": "deprecated", "label": "是否弃用", "tpl": "${deprecated ? '已弃用' : '正常'}"}, {"type": "static", "name": "kubeVersion", "label": "k8s版本要求"}]}, "actions": [{"type": "button", "label": "关闭", "close": true}, {"type": "button", "label": "安装", "level": "primary", "size": "sm", "actionType": "drawer", "close": true, "drawer": {"title": "${name} 安装配置", "size": "lg", "closeOnEsc": true, "body": {"type": "form", "mode": "horizontal", "horizontal": {"left": 1, "right": 9}, "controls": [{"type": "static", "name": "install_version", "label": "版本"}, {"type": "text", "name": "release_name", "label": "部署名称", "placeholder": "名称可选"}, {"type": "tpl", "tpl": "${ls:cluster}", "name": "release_name", "label": "集群"}, {"label": "部署空间", "type": "select", "name": "ns", "searchable": true, "required": true, "source": "/k8s/ns/option_list"}, {"type": "service", "api": "/mgm/helm/repo/${repository_name}/chart/${name}/version/${install_version}/values", "body": [{"type": "editor", "name": "values", "label": "安装参数", "language": "yaml", "value": "${yaml}", "allowFullscreen": true, "placeholder": "loading", "options": {"wordWrap": "on", "scrollbar": {"vertical": "auto"}}}]}]}, "actions": [{"type": "button", "label": "关闭", "close": true}, {"type": "button", "label": "确认安装", "level": "primary", "close": true, "actionType": "ajax", "required": ["ns"], "api": {"method": "post", "url": "/k8s/helm/release/${release_name}/repo/${repository_name}/chart/${name}/version/${install_version}/install", "data": {"values": "${values}", "ns": "${ns}"}}}]}}]}}], "toggled": true}, {"name": "name", "label": "应用", "type": "tpl", "width": "180px", "tpl": "<img src=\"${icon}\" style=\"width: 20px; height: 20px; margin-right: 5px; vertical-align: middle;\"/>${name}", "sortable": true, "searchable": {"type": "input-text", "name": "name", "clearable": true, "label": "应用", "placeholder": "输入应用名称"}}, {"name": "latest_version", "label": "最新版本", "type": "text"}, {"name": "description", "label": "描述", "type": "text", "width": "300px", "searchable": {"type": "input-text", "name": "description", "clearable": true, "label": "描述", "placeholder": "输入描述"}}, {"name": "kubeVersion", "label": "k8s版本要求", "type": "text"}, {"name": "deprecated", "label": "弃用", "type": "status"}, {"type": "text", "name": "repository_name", "label": "归属仓库", "searchable": {"type": "select", "clearable": true, "name": "repository_name", "searchable": true, "source": "/params/helm/repo/option_list"}}]}]}