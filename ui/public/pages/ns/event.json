{"type": "page", "data": {"ns": "${ls:selectedNs||'default'}", "kind": "Event", "group": "events.k8s.io", "version": "v1", "sort_by": "lastTimestamp"}, "body": [{"type": "container", "className": "floating-toolbar", "body": [{"type": "tpl", "tpl": "${kind}", "className": "mr-2"}, {"type": "button", "label": "属性文档", "level": "link", "icon": "fas fa-book-open text-primary", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind} 属性文档（ESC 关闭）", "body": [{"type": "page", "asideResizor": true, "asideSticky": false, "aside": [{"type": "input-tree", "name": "tree", "initiallyOpen": false, "unfoldedLevel": 1, "searchable": true, "showOutline": true, "showIcon": true, "searchConfig": {"sticky": true}, "heightAuto": true, "inputClassName": "no-border no-padder mt-1", "source": "get:/k8s/doc/kind/$kind/group/$group/version/$version", "onEvent": {"change": {"actions": [{"componentId": "basic", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}, {"componentId": "detail", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}]}}}], "body": [{"type": "service", "id": "basic", "body": [{"type": "tpl", "tpl": "<br><strong>属性：</strong> ${id}", "visibleOn": "${id}"}, {"type": "button", "label": "示例", "level": "link", "icon": "fas fa-lightbulb text-warning", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind}-${id} 参考样例（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/example/field", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "field": "${full_id}"}}]}, "visibleOn": "${id}"}, {"type": "tpl", "tpl": "<br><strong>类型：</strong> <span class='label label-primary'>${type}</span>", "visibleOn": "${type}"}, {"type": "tpl", "tpl": "<br><strong>描述：</strong> ${description}", "visibleOn": "${description}"}]}, {"type": "service", "id": "detail", "api": "post:/k8s/doc/detail", "body": [{"type": "divider", "title": "描述翻译", "titlePosition": "center", "visibleOn": "${translate}"}, {"type": "markdown", "value": "${translate|raw}", "options": {"linkify": true, "html": true, "breaks": true}, "visibleOn": "${translate}"}, {"type": "container", "body": [{"type": "tpl", "tpl": "<div style='height:80vh'>&nbsp</div>"}]}]}]}]}}, {"type": "button", "label": "指南", "level": "link", "icon": "fas fa-lightbulb text-primary", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind} 参考样例（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/example", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}"}}]}}]}, {"type": "container", "className": "floating-toolbar-right", "body": [{"type": "wrapper", "body": [{"type": "form", "mode": "inline", "wrapWithPanel": false, "body": [{"label": "命名空间", "type": "select", "multiple": true, "maxTagCount": 1, "name": "ns", "id": "ns", "searchable": true, "checkAll": true, "source": "/k8s/ns/option_list", "value": "${ls:selectedNs||'default'}", "onEvent": {"change": {"actions": [{"actionType": "reload", "componentId": "detailCRUD", "data": {"ns": "${ns}"}}, {"actionType": "custom", "script": "localStorage.setItem('selectedNs', event.data.ns)"}]}}}, {"label": "集群", "type": "select", "multiple": false, "name": "cluster", "id": "cluster", "searchable": true, "source": "/params/cluster/option_list", "value": "${ls:cluster}", "onEvent": {"change": {"actions": [{"actionType": "custom", "script": "localStorage.setItem('cluster', event.data.value)"}, {"actionType": "custom", "script": "window.location.reload();"}]}}}]}]}]}, {"type": "crud", "id": "detailCRUD", "name": "detailCRUD", "autoFillHeight": true, "autoGenerateFilter": {"columnsNum": 4, "showBtnToolbar": false}, "headerToolbar": [{"type": "columns-toggler", "align": "right", "draggable": true, "icon": "fas fa-cog", "overlay": true, "footerBtnSize": "sm"}, {"type": "tpl", "tpl": "共${count}条", "align": "right", "visibleOn": "${count}"}, {"type": "columns-toggler", "align": "left"}, "reload", "bulkActions"], "loadDataOnce": false, "syncLocation": false, "perPage": 10, "bulkActions": [], "api": "post:/k8s/$kind/group/$group/version/$version/list/ns/${ns}?sort_by=$sort_by", "columns": [{"type": "operation", "label": "操作", "width": 120, "buttons": [{"type": "button", "icon": "fas fa-eye text-primary", "actionType": "drawer", "tooltip": "资源描述", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "Describe: ${metadata.name}  (ESC 关闭)", "body": [{"type": "service", "api": "post:/k8s/$kind/group/$group/version/$version/describe/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "button", "label": "AI解读", "icon": "fas fa-brain text-primary", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "xl", "title": "AI解读  (ESC 关闭)", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/describe", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "name": "${metadata.name}", "namespace": "${metadata.namespace}"}}]}}, {"type": "highlightHtml", "keywords": ["Error", "Warning"], "html": "${result}"}]}]}}, {"type": "button", "icon": "fa fa-edit text-primary", "tooltip": "Yaml编辑", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "Yaml管理", "body": [{"type": "tabs", "tabsMode": "tiled", "tabs": [{"title": "查看编辑", "body": [{"type": "service", "api": "get:/k8s/$kind/group/$group/version/$version/ns/$metadata.namespace/name/$metadata.name", "body": [{"type": "mEditor", "text": "${yaml}", "componentId": "yaml", "saveApi": "/k8s/${kind}/group/${group}/version/${version}/update/ns/${metadata.namespace}/name/${metadata.name}", "options": {"language": "yaml", "wordWrap": "on", "scrollbar": {"vertical": "auto"}}}]}]}, {"title": "文档", "body": [{"type": "page", "asideResizor": true, "asideSticky": false, "aside": [{"type": "input-tree", "name": "tree", "initiallyOpen": false, "unfoldedLevel": 1, "searchable": true, "showOutline": true, "showIcon": true, "searchConfig": {"sticky": true}, "heightAuto": true, "inputClassName": "no-border no-padder mt-1", "source": "get:/k8s/doc/gvk/${apiVersion|base64Encode}/$kind", "onEvent": {"change": {"actions": [{"componentId": "basic", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}, {"componentId": "detail", "actionType": "reload", "data": {"description": "${event.data.item.description}", "id": "${event.data.item.id}", "full_id": "${event.data.item.full_id}", "type": "${event.data.item.type}"}}]}}}], "body": [{"type": "service", "id": "basic", "body": [{"type": "tpl", "tpl": "<br><strong>属性：</strong> ${id}", "visibleOn": "${id}"}, {"type": "button", "label": "示例", "level": "link", "icon": "fas fa-lightbulb text-warning", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind}-${id} 参考样例（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/example/field", "params": {"kind": "${kind}", "group": "${group}", "version": "${version}", "field": "${full_id}"}}]}, "visibleOn": "${id}"}, {"type": "tpl", "tpl": "<br><strong>类型：</strong> <span class='label label-primary'>${type}</span>", "visibleOn": "${type}"}, {"type": "tpl", "tpl": "<br><strong>描述：</strong> ${description}", "visibleOn": "${description}"}]}, {"type": "service", "id": "detail", "api": "post:/k8s/doc/detail", "body": [{"type": "divider", "title": "描述翻译", "titlePosition": "center", "visibleOn": "${translate}"}, {"type": "markdown", "value": "${translate|raw}", "options": {"linkify": true, "html": true, "breaks": true}, "visibleOn": "${translate}"}, {"type": "container", "body": [{"type": "tpl", "tpl": "<div style='height:80vh'>&nbsp</div>"}]}]}]}]}]}], "actions": []}}, {"type": "button", "icon": "fas fa-brain text-primary", "tooltip": "AI问诊", "actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "AI 查询", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/event", "params": {"note": "${note}", "source": "${source}", "reason": "${reason}", "reportingController": "${reportingController}", "type": "${type}", "regardingKind": "${kind}"}}]}, "visibleOn": "type === 'Warning'"}], "toggled": true}, {"name": "type", "label": "类型", "searchable": {"type": "select", "clearable": true, "options": [{"label": "正常 ", "value": "Normal"}, {"label": "告警 ", "value": "Warning"}]}, "filterable": {"options": [{"label": "正常 ", "value": "Normal"}, {"label": "告警 ", "value": "Warning"}]}, "type": "mapping", "map": {"Normal": "<span class='label label-info'>正常</span>", "Warning": "<span class='label label-danger'>告警</span>"}}, {"name": "reason", "label": "原因", "type": "mapping", "map": {"ApplyJob": "<span class='label label-primary'>执行任务</span>", "AppliedDaemonSet": "<span class='label label-primary'>已应用守护进程集</span>", "ApplyingManifest": "<span class='label label-primary'>正在应用清单</span>", "AppliedManifest": "<span class='label label-success'>已应用清单</span>", "AmbiguousSelector": "<span class='label label-warning'>选择器模糊</span>", "BackOff": "<span class='label label-danger'>退避中</span>", "CrashLoopBackOff": "<span class='label label-danger'>崩溃循环退避</span>", "Created": "<span class='label label-primary'>已创建</span>", "Completed": "<span class='label label-primary'>已完成</span>", "EnsuringLoadBalancer": "<span class='label label-primary'>负载确认</span>", "Evicted": "<span class='label label-danger'>已驱逐</span>", "ErrImagePull": "<span class='label label-danger'>镜像拉取错误</span>", "ExceededGracePeriod": "<span class='label label-danger'>超过宽限期</span>", "Failed": "<span class='label label-danger'>失败</span>", "FailedAttachVolume": "<span class='label label-danger'>挂载卷失败</span>", "FailedBindVolume": "<span class='label label-danger'>卷绑定失败</span>", "FailedComputeMetricsReplicas": "<span class='label label-danger'>计算指标副本失败</span>", "FailedCreate": "<span class='label label-danger'>创建失败</span>", "FailedCreatePodContainer": "<span class='label label-danger'>创建 Pod 容器失败</span>", "FailedCreatePodSandBox": "<span class='label label-danger'>创建 Pod 沙箱失败</span>", "FailedGetContainerResourceMetric": "<span class='label label-danger'>获取容器资源指标失败</span>", "FailedGetExternalMetric": "<span class='label label-danger'>获取外部指标失败</span>", "FailedGetObjectMetric": "<span class='label label-danger'>获取对象指标失败</span>", "FailedGetPodsMetric": "<span class='label label-danger'>获取Pod指标失败</span>", "FailedKillPod": "<span class='label label-danger'>终止Pod失败</span>", "FailedMount": "<span class='label label-danger'>挂载失败</span>", "FailedSync": "<span class='label label-danger'>同步失败</span>", "FailedScheduling": "<span class='label label-danger'>调度失败</span>", "FailedVolumeResize": "<span class='label label-danger'>卷调整大小失败</span>", "FailedToUpdateEndpoint": "<span class='label label-danger'>端点更新失败</span>", "FailedToUpdateEndpointSlices": "<span class='label label-danger'>端点切片更新失败</span>", "FailedGetResourceMetric": "<span class='label label-danger'>获取资源指标失败</span>", "HeadlessServiceNoSelector": "<span class='label label-warning'>无头服务无选择器</span>", "ImageInspectError": "<span class='label label-danger'>镜像检查错误</span>", "ImagePullBackOff": "<span class='label label-danger'>镜像拉取退避</span>", "Killing": "<span class='label label-danger'>终止中</span>", "LeaderElection": "<span class='label label-primary'>主节点选举</span>", "MultiplePodDisruptionBudgets": "<span class='label label-danger'>多个Pod中断预算</span>", "MissingJob": "<span class='label label-danger'>任务错过</span>", "NetworkNotReady": "<span class='label label-warning'>网络未就绪</span>", "NetworkPluginNotReady": "<span class='label label-warning'>网络插件未准备好</span>", "NodeAllocatableEnforced": "<span class='label label-primary'>节点可分配资源已强制执行</span>", "NodePasswordValidationComplete": "<span class='label label-primary'>节点密码验证完成</span>", "NodeHasDiskPressure": "<span class='label label-danger'>节点磁盘压力</span>", "NodeHasMemoryPressure": "<span class='label label-danger'>节点内存压力</span>", "NodeHasNoDiskPressure": "<span class='label label-success'>节点磁盘无压力</span>", "NodeHasSufficientMemory": "<span class='label label-success'>节点内存充足</span>", "NodeHasSufficientPID": "<span class='label label-success'>节点PID充足</span>", "NodeLost": "<span class='label label-danger'>节点丢失</span>", "NodeSchedulable": "<span class='label label-primary'>节点可调度</span>", "NodeReady": "<span class='label label-success'>节点已就绪</span>", "NodeNotSchedulable": "<span class='label label-danger'>节点不可调度</span>", "NodeNotReady": "<span class='label label-danger'>节点未就绪</span>", "ProvisioningFailed": "<span class='label label-danger'>卷供应失败</span>", "Preempting": "<span class='label label-warning'>抢占中</span>", "ProgressDeadlineExceeded": "<span class='label label-danger'>处理超时</span>", "Pulled": "<span class='label label-success'>已拉取</span>", "Pulling": "<span class='label label-primary'>拉取中</span>", "Rebooted": "<span class='label label-danger'>重启</span>", "RegisteredNode": "<span class='label label-success'>节点注册</span>", "RegistryUnavailable": "<span class='label label-danger'>镜像仓库不可用</span>", "ReplicaFailure": "<span class='label label-danger'>副本失败</span>", "RunContainerError": "<span class='label label-danger'>运行容器错误</span>", "SandboxChanged": "<span class='label label-danger'>沙箱改变</span>", "ScalingReplicaSet": "<span class='label label-primary'>副本集扩缩容</span>", "Scheduled": "<span class='label label-primary'>已调度</span>", "ServiceExternalIPClaimed": "<span class='label label-warning'>服务外部IP已被占用</span>", "Started": "<span class='label label-success'>已启动</span>", "Starting": "<span class='label label-primary'>启动中</span>", "SuccessfulCreate": "<span class='label label-success'>创建成功</span>", "SuccessfulDelete": "<span class='label label-success'>删除成功</span>", "Synced": "<span class='label label-success'>同步成功</span>", "SawCompletedJob": "<span class='label label-success'>任务已完成</span>", "TaintManagerEviction": "<span class='label label-warning'>污点驱逐</span>", "UpdatedLoadBalancer": "<span class='label label-primary'>负载均衡器更新</span>", "Unhealthy": "<span class='label label-danger'>不健康</span>", "VolumeBindConflict": "<span class='label label-danger'>卷绑定冲突</span>", "VolumeInUseByPod": "<span class='label label-danger'>卷被Pod使用中</span>", "WaitForFirstConsumer": "<span class='label label-warning'>等待首个消费者</span>", "*": "<span class='label label-warning'>${reason}</span>"}}, {"name": "note", "label": "说明", "type": "text", "searchable": true}, {"name": "object", "label": "关联对象", "type": "tpl", "tpl": "${regarding.kind}/${regarding.name}"}, {"name": "field", "label": "关联字段", "type": "tpl", "tpl": "${regarding.fieldPath}"}, {"name": "source", "label": "事件来源", "type": "tpl", "tpl": "${reportingController} ${reportingInstance}"}, {"name": "metadata.namespace", "label": "命名空间", "type": "text", "sortable": true}, {"name": "metadata.name", "label": "名称", "type": "text", "width": "180px", "sortable": true, "searchable": {"type": "input-text", "name": "metadata.name", "clearable": true, "label": "名称", "placeholder": "输入名称"}}, {"name": "metadata.creationTimestamp", "label": "存在时长", "type": "k8sAge"}]}]}