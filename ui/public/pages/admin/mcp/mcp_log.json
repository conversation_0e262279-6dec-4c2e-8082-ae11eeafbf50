{"type": "page", "data": {"kind": "MCP执行记录", "group": "", "version": ""}, "body": [{"type": "container", "className": "floating-toolbar", "body": [{"type": "tpl", "tpl": "${kind}", "className": "mr-2"}]}, {"type": "crud", "api": "/admin/mcp/log/list", "autoFillHeight": true, "autoGenerateFilter": {"columnsNum": 4, "showBtnToolbar": false}, "headerToolbar": [{"type": "columns-toggler", "align": "right", "draggable": true, "icon": "fas fa-cog", "overlay": true, "footerBtnSize": "sm"}, {"type": "tpl", "tpl": "共${count}条", "align": "right", "visibleOn": "${count}"}, {"type": "columns-toggler", "align": "left"}, "reload"], "loadDataOnce": false, "syncLocation": false, "perPage": 10, "columns": [{"name": "id", "label": "ID", "sortable": true}, {"name": "created_at", "label": "执行时间", "type": "datetime", "sortable": true}, {"name": "server_name", "label": "服务器名称", "searchable": true}, {"name": "tool_name", "label": "工具名称", "searchable": true}, {"name": "result", "label": "执行结果", "type": "tpl", "tpl": "详情", "className": "cursor-pointer", "onEvent": {"click": {"actions": [{"actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "title": "MCP工具执行详情", "size": "lg", "body": {"type": "form", "body": [{"type": "static", "label": "发起人", "value": "${created_by}"}, {"type": "static", "label": "对话内容", "value": "${prompt}"}, {"type": "static", "label": "服务器名称", "value": "${server_name}"}, {"type": "static", "label": "工具名称", "value": "${tool_name}"}, {"type": "static", "label": "调用参数", "value": "${parameters|raw}", "levelExpand": 3}, {"type": "static", "label": "执行结果", "value": "${result|raw}"}, {"type": "static", "label": "执行耗时", "value": "${execute_time}ms"}]}}}]}}}, {"name": "duration", "label": "执行耗时", "type": "tpl", "tpl": "${execute_time}ms"}, {"name": "created_by", "label": "发起人"}, {"name": "error", "label": "错误信息", "type": "tpl", "tpl": "${error}", "className": "text-danger"}]}]}