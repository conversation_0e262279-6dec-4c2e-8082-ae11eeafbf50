{"type": "page", "data": {"kind": "MCP", "group": "", "version": ""}, "body": [{"type": "container", "className": "floating-toolbar", "body": [{"type": "tpl", "tpl": "${kind}", "className": "mr-2"}, {"type": "button", "label": "指南", "level": "link", "icon": "fas fa-lightbulb text-primary", "actionType": "drawer", "drawer": {"overlay": false, "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "${kind} 参考样例（ESC 关闭）", "body": [{"type": "websocketMarkdownViewer", "url": "/ai/chat/any_selection", "params": {"question": "MCP(Model Context Protocol，模型上下文协议) 是一种新的开放标准协议，用来在大模型和数据源之间建立安全双向的链接。MCP 遵循客户端-服务器架构（client-server），其中包含以下几个核心概念：MCP 主机（MCP Hosts）：发起请求的 LLM 应用程序（例如 Claude Desktop、IDE 或 AI 工具）。MCP 客户端（MCP Clients）：在主机程序内部，与 MCP server 保持 1: 1 的连接。MCP 服务器（MCP Servers）：为 MCP client 提供上下文、工具和 prompt 信息。本地资源（Local Resources）：本地计算机中可供 MCP server 安全访问的资源（例如文件、数据库）。远程资源（Remote Resources）：MCP server 可以连接到的远程资源（例如通过 API）。请参考上面的信息，详细解释MCP（Model Context Protocol，模型上下文协议）原理及使用场景、使用方法及注意事项。这里MCP跟k8s没有关系，不用关联。"}}]}}]}, {"type": "alert", "level": "success", "body": "温馨提示：MCP服务器启用后，在大模型对话中，会自动注册使用。"}, {"type": "crud", "api": "/admin/mcp/list", "quickSaveItemApi": "/admin/mcp/save/id/${id}/status/${enabled}", "autoFillHeight": true, "autoGenerateFilter": {"columnsNum": 4, "showBtnToolbar": false}, "headerToolbar": [{"type": "columns-toggler", "align": "right", "draggable": true, "icon": "fas fa-cog", "overlay": true, "footerBtnSize": "sm"}, {"type": "tpl", "tpl": "共${count}条", "align": "right", "visibleOn": "${count}"}, {"type": "columns-toggler", "align": "left"}, "reload", "bulkActions", {"type": "button", "label": "添加服务器", "icon": "fa fa-plus", "actionType": "dialog", "dialog": {"title": "添加服务器", "body": {"type": "form", "api": "/admin/mcp/save", "body": [{"type": "alert", "level": "success", "body": "温馨提示：当前支持SSE类型服务器，其他类型服务器暂不支持。"}, {"type": "input-text", "name": "name", "label": "服务器名称", "required": true}, {"type": "input-text", "name": "url", "label": "服务器地址", "required": true}, {"type": "switch", "name": "enabled", "label": "是否启用", "value": true}]}}}, {"type": "button", "label": "MCP导航", "icon": "fa fa-compass", "actionType": "drawer", "drawer": {"title": "MCP导航", "size": "md", "body": [{"type": "link", "href": "https://mcp.so/servers", "body": "mcp.so", "htmlTarget": "_blank"}]}}], "bulkActions": [{"label": "批量删除", "actionType": "ajax", "confirmText": "确定要批量删除?", "api": {"url": "/admin/mcp/delete", "method": "post", "data": {"ids": "${selectedItems | pick:id }"}}}], "loadDataOnce": false, "syncLocation": false, "perPage": 10, "columns": [{"name": "name", "label": "服务器名称"}, {"name": "url", "label": "服务器地址"}, {"name": "enabled", "label": "启用", "quickEdit": {"mode": "inline", "type": "switch", "onText": "开启", "offText": "关闭", "saveImmediately": true, "resetOnFailed": true}}, {"name": "tools", "label": "工具", "type": "tpl", "tpl": "详情", "className": "cursor-pointer", "onEvent": {"click": {"actions": [{"actionType": "drawer", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "title": "MCP服务器工具信息 - ${name}", "size": "lg", "body": {"type": "crud", "id": "toolsCRUD", "name": "toolsCRUD", "autoFillHeight": true, "api": "get:/admin/mcp/server/${name}/tools/list", "quickSaveItemApi": "/admin/mcp/tool/save/id/${id}/status/${enabled}", "headerToolbar": [{"type": "columns-toggler", "align": "right", "draggable": true, "icon": "fas fa-cog", "overlay": true, "footerBtnSize": "sm"}, {"type": "tpl", "tpl": "共${count}条", "align": "right", "visibleOn": "${count}"}, {"type": "columns-toggler", "align": "left"}, "reload"], "loadDataOnce": true, "syncLocation": false, "initFetch": true, "perPage": 10, "columns": [{"name": "name", "label": "名称", "sortable": true, "searchable": {"type": "input-text", "name": "name", "label": "工具名称", "placeholder": "输入工具名称", "width": "200px"}, "type": "tpl", "tpl": "${name}<br><span class='text-muted'>${description}</span>"}, {"name": "enabled", "label": "启用", "quickEdit": {"mode": "inline", "type": "switch", "onText": "开启", "offText": "关闭", "saveImmediately": true, "resetOnFailed": true}}, {"name": "name", "label": "详情", "type": "tpl", "tpl": "参数", "className": "cursor-pointer", "onEvent": {"click": {"actions": [{"actionType": "drawer", "drawer": {"title": "工具信息 - ${name}", "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "body": {"type": "form", "mode": "horizontal", "body": [{"type": "tpl", "tpl": "用途:<br>${description|raw}"}, {"type": "tpl", "tpl": "<br>参数:<br>"}, {"type": "json", "label": "输入参数", "value": "${input_schema|toJson|pick:properties}", "levelExpand": 3, "className": "m-1"}]}}}]}}}]}}}]}}}, {"type": "operation", "label": "操作", "buttons": [{"type": "button", "label": "编辑", "actionType": "dialog", "dialog": {"title": "编辑服务器", "body": {"type": "form", "api": "post:/admin/mcp/save", "body": [{"type": "hidden", "name": "id", "required": true}, {"type": "input-text", "name": "name", "label": "服务器名称", "required": true}, {"type": "input-text", "name": "url", "label": "服务器地址", "required": true}, {"type": "switch", "name": "enabled", "label": "是否启用"}]}}}]}]}]}