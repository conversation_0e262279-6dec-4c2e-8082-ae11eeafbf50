{"type": "page", "title": "开放MCP服务", "remark": {"body": "将k8m内置的MCP开放给其他大模型或软件使用，请妥善保管认证Token。该Token将使用您的访问权限", "icon": "question-mark", "placement": "right", "trigger": "click", "rootClose": true}, "body": [{"type": "button", "label": "查看使用说明", "level": "link", "className": "text-info", "actionType": "dialog", "dialog": {"title": "MCP服务使用说明（ESC 关闭）", "closeOnEsc": true, "closeOnOutside": true, "size": "lg", "body": "<div><p><li>如使用NodePort方式访问，请将域名替换为集群节点IP，端口替换为NodePort端口</li><li>如使用网关方式访问，请将域名和端口替换为网关地址</li><li>如您不能确定访问方式，可联系系统管理员</li><li>动态路径、Auth Token 跟您的访问权限进行了绑定，请勿分享给他人</li></p></li></div>"}}, {"type": "crud", "id": "apiKeysCRUD", "name": "apiKeysCRUD", "autoFillHeight": true, "api": "get:/mgm/user/profile/mcp_keys/list", "headerToolbar": [{"type": "button", "label": "创建密钥", "level": "primary", "actionType": "dialog", "dialog": {"title": "创建访问密钥", "closeOnEsc": true, "closeOnOutside": true, "body": {"type": "form", "api": "post:/mgm/user/profile/mcp_keys/create", "body": [{"type": "input-text", "name": "description", "label": "描述信息", "required": true, "placeholder": "请输入访问链接用途描述"}]}}}, "reload"], "columns": [{"type": "operation", "label": "操作", "buttons": [{"type": "button", "label": "删除", "level": "link", "className": "text-danger", "confirmText": "确认要删除该密钥吗？", "actionType": "ajax", "api": "delete:/mgm/user/profile/mcp_keys/delete/${id}"}]}, {"name": "mcp_key", "label": "密钥ID", "type": "tpl", "tpl": "${mcp_key|truncate:6}"}, {"name": "visit", "label": "使用方法", "type": "control", "width": "150px", "body": [{"type": "button", "label": "点击查看", "level": "link", "actionType": "dialog", "dialog": {"closeOnEsc": true, "closeOnOutside": true, "size": "lg", "title": "访问方式", "body": "<div><p><strong>方式一: 动态地址</strong></p><ul style='margin-left:20px'><code><%= window.location.protocol + '//' + window.location.hostname + (window.location.port ? ':' + window.location.port : '') %>/mcp/k8m/<%= data.mcp_key%>/sse</code></ul><p><strong>方式二：静态地址+Header</strong></p><ul style='margin-left:20px'><p><code><%= window.location.protocol + '//' + window.location.hostname + (window.location.port ? ':' + window.location.port : '') %>/mcp/k8m/sse</code></p><li>在HTTP请求头中添加Header：<br><code>Authorization: Bearer <%=data.jwt%></code></li></ul><p><strong>示例配置（JSON）</strong></p><pre style='background:#f8f8f8;border:1px solid #ccc;padding:10px;border-radius:4px;'><code>{\n  \"mcpServers\": {\n    \"k8m\": {\n      \"url\": \"<%= window.location.protocol + '//' + window.location.hostname + (window.location.port ? ':' + window.location.port : '') %>/mcp/k8m/<%= data.mcp_key %>/sse\"\n    }\n  }\n}</code></pre></div>"}}]}, {"name": "description", "label": "描述信息"}, {"name": "created_at", "label": "创建时间", "type": "datetime"}]}]}