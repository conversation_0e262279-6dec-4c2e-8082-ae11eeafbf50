{"type": "page", "title": "登录设置", "body": [{"type": "crud", "id": "detailCRUD", "name": "detailCRUD", "headerToolbar": [{"type": "columns-toggler", "align": "right", "draggable": true, "icon": "fas fa-cog", "overlay": true, "footerBtnSize": "sm"}, {"type": "tpl", "tpl": "共${count}条", "align": "right", "visibleOn": "${count}"}, {"type": "columns-toggler", "align": "left"}, "reload"], "autoFillHeight": true, "api": "get:/mgm/user/profile", "columns": [{"type": "operation", "label": "操作", "buttons": [{"type": "button", "actionType": "drawer", "label": "修改密码", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "title": "修改密码  (ESC 关闭)", "body": {"type": "form", "body": [{"type": "passwordEditor", "api": "/mgm/user/profile/update_psw"}], "submitText": "保存", "resetText": "重置", "messages": {"saveSuccess": "密码设置成功", "saveFailed": "密码设置失败"}, "onEvent": {"submitSucc": {"actions": [{"actionType": "reload", "componentId": "profileCRUD"}, {"actionType": "closeDrawer"}]}}}}}]}, {"name": "username", "label": "用户名", "type": "text"}, {"label": "用户组", "type": "text", "name": "group_names"}, {"name": "two_fa_enabled", "label": "2步验证", "type": "container", "body": [{"type": "tpl", "tpl": "${two_fa_enabled ? `<span class=\"text-success\"><i class=\"fas fa-shield-alt\"></i> ${two_fa_type || 'TOTP'} ${two_fa_app_name}</span>` : `<span class=\"text-muted\"><i class=\"fas fa-shield-alt\"></i> 未启用</span>`}"}, {"type": "button", "actionType": "ajax", "label": "关闭", "level": "link", "confirmText": "确定要关闭2步验证吗？关闭后将不再需要验证码登录。", "api": "post:/mgm/user/profile/2fa/disable", "visibleOn": "two_fa_enabled===true", "onEvent": {"success": {"actions": [{"actionType": "reload", "componentId": "detailCRUD"}]}}}, {"type": "button", "actionType": "drawer", "label": "绑定", "level": "link", "drawer": {"closeOnEsc": true, "closeOnOutside": true, "title": "2步验证  (ESC 关闭)", "body": {"type": "form", "api": "post:/mgm/user/profile/2fa/enable", "initApi": "post:/mgm/user/profile/2fa/generate", "body": [{"name": "qr-code", "type": "qr-code", "codeSize": 128, "value": "${qr_url}", "label": "请使用身份验证器扫描二维码", "className": "text-center"}, {"type": "input-text", "name": "code", "label": "验证码", "required": true, "placeholder": "请输入验证码"}, {"type": "input-text", "name": "app_name", "label": "应用名称", "required": true, "placeholder": "软件名称", "description": "为此验证器起个名字，避免忘记绑定到哪个软件上了"}, {"type": "tpl", "tpl": "<div class='alert alert-info'><p><strong>绑定说明：</strong></p><ol><li>请先下载并安装支持的身份验证器应用：<ul><li>Google Authenticator (<a href='https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2' target='_blank'>Android</a> / <a href='https://apps.apple.com/app/google-authenticator/id388497605' target='_blank'>iOS</a>)</li><li>Microsoft Authenticator (<a href='https://play.google.com/store/apps/details?id=com.azure.authenticator' target='_blank'>Android</a> / <a href='https://apps.apple.com/app/microsoft-authenticator/id983156458' target='_blank'>iOS</a>)</li><li>其他支持TOTP协议的身份验证器应用也可使用，如Authy、1Password等</li></ul></li><li>打开验证器应用，点击添加账号或扫描二维码</li><li>使用验证器扫描上方显示的二维码</li><li>等待验证器生成6位数验证码</li><li>将验证码输入上方输入框进行验证</li><li>验证成功后，每次登录时都需要输入验证器生成的验证码</li></ol><p><strong>注意：</strong>请妥善保管验证器，如果手机丢失或更换设备，将无法登录系统。建议备份验证器的恢复码。</p></div>"}], "submitText": "绑定", "messages": {"saveSuccess": "绑定成功", "saveFailed": "绑定失败"}, "onEvent": {"submitSucc": {"actions": [{"actionType": "reload", "componentId": "detailCRUD"}, {"actionType": "closeDrawer"}]}}}}}]}, {"label": "来源", "type": "text", "name": "source"}, {"name": "created_at", "label": "创建时间", "type": "datetime"}]}]}