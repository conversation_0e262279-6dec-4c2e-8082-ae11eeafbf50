// GENERATED BY THE COMMAND ABOVE; DO NOT EDIT
// This file was generated by swaggo/swag

package swagger

import (
	"bytes"
	"encoding/json"
	"strings"

	"github.com/alecthomas/template"
	"github.com/swaggo/swag"
)

var doc = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{.Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/admin/ai/model/delete/{ids}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "删除AI模型配置",
                "parameters": [
                    {
                        "type": "string",
                        "description": "模型ID，多个用逗号分隔",
                        "name": "ids",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/ai/model/id/{id}/think/{status}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "快速保存AI模型思考状态",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "模型ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "状态，例如：true、false",
                        "name": "status",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/ai/model/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取AI模型配置列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/ai/model/save": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "创建或更新AI模型配置",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/ai/model/test/id/{id}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "测试AI模型连接",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "模型ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/cluster/file/option_list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取所有已发现集群的kubeconfig文件名列表，用于下拉选项",
                "summary": "获取文件类型的集群选项",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/cluster/kubeconfig/remove": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "从数据库中删除KubeConfig配置",
                "summary": "删除KubeConfig",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/cluster/kubeconfig/save": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "保存KubeConfig配置到数据库",
                "summary": "保存KubeConfig",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/cluster/scan": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "扫描本地Kubeconfig文件目录以发现新的集群",
                "summary": "扫描集群",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/cluster/{cluster}/disconnect": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "断开一个正在运行的集群的连接",
                "summary": "断开集群连接",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Base64编码的集群ID",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "已执行，请稍后刷新",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/cluster_permissions/cluster/{cluster}/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取指定集群下所有用户的权限角色列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群ID(base64)",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/cluster_permissions/cluster/{cluster}/ns/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取指定集群下所有命名空间名称",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群ID(base64)",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/cluster_permissions/cluster/{cluster}/role/{role}/user/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取指定集群指定角色的用户权限列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群ID(base64)",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "角色",
                        "name": "role",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/cluster_permissions/cluster/{cluster}/role/{role}/{authorization_type}/save": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "批量为指定集群添加用户角色权限",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群ID(base64)",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "角色",
                        "name": "role",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "授权类型",
                        "name": "authorization_type",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/cluster_permissions/delete/{ids}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "删除集群权限",
                "parameters": [
                    {
                        "type": "string",
                        "description": "权限ID，多个用逗号分隔",
                        "name": "ids",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/cluster_permissions/update_blacklist_namespaces/{id}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "更新指定集群用户角色的黑名单命名空间字段",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "权限ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/cluster_permissions/update_namespaces/{id}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "更新指定集群用户角色的命名空间字段",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "权限ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/cluster_permissions/user/{username}/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取指定用户已获得授权的集群",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户名",
                        "name": "username",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/condition/delete/{ids}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "删除条件",
                "parameters": [
                    {
                        "type": "string",
                        "description": "条件ID，多个用逗号分隔",
                        "name": "ids",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/condition/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取条件列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/condition/save": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "创建或更新条件",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/condition/save/id/{id}/status/{status}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "快速保存条件状态",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "条件ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "状态，例如：true、false",
                        "name": "status",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/config/all": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取系统配置",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/config/sso/delete/{ids}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "删除SSO配置",
                "parameters": [
                    {
                        "type": "string",
                        "description": "SSO配置ID，多个用逗号分隔",
                        "name": "ids",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/config/sso/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取SSO配置列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/config/sso/save": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "创建或更新SSO配置",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/config/sso/save/id/{id}/status/{enabled}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "快速更新SSO配置状态",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "SSO配置ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "状态，例如：true、false",
                        "name": "enabled",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/config/update": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "更新系统配置",
                "parameters": [
                    {
                        "description": "配置信息",
                        "name": "config",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.Config"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/helm/repo/delete/{ids}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "删除一个或多个Helm仓库",
                "summary": "删除Helm仓库",
                "parameters": [
                    {
                        "type": "string",
                        "description": "要删除的仓库ID，多个用逗号分隔",
                        "name": "ids",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "操作成功",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/helm/repo/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取所有Helm仓库信息",
                "summary": "Helm仓库列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/helm/repo/option_list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取所有Helm仓库名称，用于下拉选项",
                "summary": "Helm仓库选项列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/helm/repo/save": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "添加或更新一个Helm仓库信息",
                "summary": "添加或更新Helm仓库",
                "parameters": [
                    {
                        "description": "Helm仓库信息",
                        "name": "repo",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.HelmRepository"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "操作成功",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/helm/repo/update_index": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新指定Helm仓库的索引信息",
                "summary": "更新Helm仓库索引",
                "parameters": [
                    {
                        "description": "要更新索引的仓库ID，多个用逗号分隔",
                        "name": "ids",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "操作成功",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/inspection/event/status/option_list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取巡检事件状态选项列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/inspection/record/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据巡检计划ID获取对应的巡检记录列表",
                "summary": "获取巡检记录列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "巡检计划ID",
                        "name": "id",
                        "in": "path"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/inspection/schedule/delete/{ids}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "删除巡检计划",
                "parameters": [
                    {
                        "type": "string",
                        "description": "巡检计划ID，多个用逗号分隔",
                        "name": "ids",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/inspection/schedule/id/{id}/summary/cluster/{cluster}/start_time/{start_time}/end_time/{end_time}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "统计指定巡检计划的执行情况，支持按时间范围和集群过滤",
                "summary": "统计巡检计划执行情况",
                "parameters": [
                    {
                        "type": "string",
                        "description": "巡检计划ID",
                        "name": "id",
                        "in": "path"
                    },
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path"
                    },
                    {
                        "type": "string",
                        "description": "开始时间(RFC3339格式)",
                        "name": "start_time",
                        "in": "path"
                    },
                    {
                        "type": "string",
                        "description": "结束时间(RFC3339格式)",
                        "name": "end_time",
                        "in": "path"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/inspection/schedule/id/{id}/update_script_code": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "更新巡检脚本代码",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "巡检计划ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "脚本代码",
                        "name": "script_codes",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/inspection/schedule/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取巡检计划列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/inspection/schedule/record/id/{id}/event/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取巡检事件列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "巡检记录ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/inspection/schedule/record/id/{id}/output/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取巡检脚本输出列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "巡检记录ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/inspection/schedule/record/id/{id}/push": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "将指定巡检记录的AI总结推送到所有配置的Webhook接收器",
                "summary": "推送巡检记录",
                "parameters": [
                    {
                        "type": "string",
                        "description": "巡检记录ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/inspection/schedule/record/id/{id}/summary": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "为指定巡检记录生成AI总结",
                "summary": "生成巡检记录AI总结",
                "parameters": [
                    {
                        "type": "string",
                        "description": "巡检记录ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/inspection/schedule/save": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "保存巡检计划",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/inspection/schedule/save/id/{id}/status/{enabled}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "快速更新巡检计划状态",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "巡检计划ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "状态，例如：true、false",
                        "name": "enabled",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/inspection/schedule/start/id/{id}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "启动巡检计划",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "巡检计划ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/inspection/script/delete/{ids}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "删除Lua脚本",
                "parameters": [
                    {
                        "type": "string",
                        "description": "脚本ID，多个用逗号分隔",
                        "name": "ids",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/inspection/script/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Lua脚本列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/inspection/script/load": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "加载内置Lua脚本",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/inspection/script/option_list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Lua脚本选项列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/inspection/script/save": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "保存Lua脚本",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/inspection/webhook/delete/{ids}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "删除Webhook接收器",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Webhook接收器ID，多个用逗号分隔",
                        "name": "ids",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/inspection/webhook/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Webhook接收器列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/inspection/webhook/option_list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Webhook接收器选项列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/inspection/webhook/save": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "创建或更新Webhook接收器",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/mcp/connect/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "连接指定MCP服务器",
                "parameters": [
                    {
                        "type": "string",
                        "description": "MCP服务器名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/mcp/delete": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "删除MCP服务器",
                "parameters": [
                    {
                        "description": "删除请求体包含IDs数组",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/mcp/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取MCP服务器列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/mcp/log/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取MCP服务器日志列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/mcp/save": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "创建或更新MCP服务器",
                "parameters": [
                    {
                        "description": "MCP服务器配置信息",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.MCPServerConfig"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/mcp/save/id/{id}/status/{status}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "快速更新MCP服务器状态",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "MCP服务器ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "服务器状态(true/false)",
                        "name": "status",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/mcp/server/{name}/tools/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取指定MCP服务器的工具列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "MCP服务器名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/mcp/tool/save/id/{id}/status/{status}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "快速更新MCP工具状态",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "工具ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "状态，例如：true、false",
                        "name": "status",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/user/2fa/disable/{id}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "禁用指定用户的二步验证",
                "summary": "禁用用户2FA",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/user/delete/{ids}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID批量删除用户",
                "summary": "删除用户",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户ID，多个用逗号分隔",
                        "name": "ids",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/user/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取所有用户信息",
                "summary": "获取用户列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.User"
                            }
                        }
                    }
                }
            }
        },
        "/admin/user/option_list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取用户选项列表，用于下拉选择",
                "summary": "获取用户选项列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/admin/user/save": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "新增或更新用户信息",
                "consumes": [
                    "application/json"
                ],
                "summary": "保存用户",
                "parameters": [
                    {
                        "description": "用户信息",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.User"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/admin/user/save/id/{id}/status/{disabled}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID快速更新用户启用/禁用状态",
                "summary": "快速更新用户状态",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "状态，例如：true、false",
                        "name": "disabled",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/user/update_psw/{id}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID更新用户密码",
                "consumes": [
                    "application/json"
                ],
                "summary": "更新用户密码",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "新密码信息",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.User"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/user_group/delete/{ids}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID批量删除用户组",
                "summary": "删除用户组",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户组ID，多个用逗号分隔",
                        "name": "ids",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/admin/user_group/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取所有用户组信息",
                "summary": "获取用户组列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.UserGroup"
                            }
                        }
                    }
                }
            }
        },
        "/admin/user_group/option_list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取所有用户组的选项（仅ID和名称）",
                "summary": "用户组选项列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "additionalProperties": {
                                    "type": "string"
                                }
                            }
                        }
                    }
                }
            }
        },
        "/admin/user_group/save": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "新增或更新用户组信息",
                "consumes": [
                    "application/json"
                ],
                "summary": "保存用户组",
                "parameters": [
                    {
                        "description": "用户组信息",
                        "name": "data",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.UserGroup"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/ai/chat/any_question": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "回答K8s相关问题",
                "parameters": [
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "问题内容",
                        "name": "question",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/ai/chat/any_selection": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "解释选择内容",
                "parameters": [
                    {
                        "type": "string",
                        "description": "要解释的内容",
                        "name": "question",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/ai/chat/cron": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "分析Cron表达式",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Cron表达式",
                        "name": "cron",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/ai/chat/describe": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "分析K8s资源描述",
                "parameters": [
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "namespace",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/ai/chat/event": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "分析K8s事件",
                "parameters": [
                    {
                        "type": "string",
                        "description": "事件备注",
                        "name": "note",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "事件来源",
                        "name": "source",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "事件原因",
                        "name": "reason",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "事件类型",
                        "name": "type",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "相关资源类型",
                        "name": "regardingKind",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/ai/chat/example": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取K8s资源使用示例",
                "parameters": [
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/ai/chat/example/field": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取K8s资源字段示例",
                "parameters": [
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "字段名称",
                        "name": "field",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/ai/chat/gptshell": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "通过WebSocket提供GPT交互式对话终端",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "namespace",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "resource",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "对话内容",
                        "name": "content",
                        "in": "query"
                    }
                ],
                "responses": {
                    "101": {
                        "description": "Switching Protocols",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/ai/chat/history": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取聊天历史记录",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/ai/chat/k8s_gpt/resource": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "K8s错误信息分析",
                "parameters": [
                    {
                        "type": "string",
                        "description": "错误内容",
                        "name": "data",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "相关字段",
                        "name": "field",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/ai/chat/log": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "分析日志",
                "parameters": [
                    {
                        "type": "string",
                        "description": "日志内容",
                        "name": "data",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/ai/chat/reset": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "重置聊天历史记录",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/ai/chat/resource": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取K8s资源使用指南",
                "parameters": [
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/auth/ldap/config": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取系统LDAP登录开关状态",
                "summary": "获取LDAP开关状态",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/auth/login": {
            "post": {
                "description": "用户通过用户名、密码和2FA验证码登录，支持普通和LDAP登录",
                "summary": "用户登录",
                "parameters": [
                    {
                        "description": "用户名",
                        "name": "username",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    },
                    {
                        "description": "密码（加密）",
                        "name": "password",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    },
                    {
                        "description": "登录类型 0:普通 1:LDAP",
                        "name": "loginType",
                        "in": "body",
                        "schema": {
                            "type": "integer"
                        }
                    },
                    {
                        "description": "2FA验证码",
                        "name": "code",
                        "in": "body",
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "登录成功，返回JWT Token",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "401": {
                        "description": "登录失败",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/auth/oidc/{name}/callback": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "处理OIDC认证后的回调，完成用户登录",
                "summary": "处理OIDC回调",
                "parameters": [
                    {
                        "type": "string",
                        "description": "SSO名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "认证代码",
                        "name": "code",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/auth/oidc/{name}/sso": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    },
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取指定SSO名称的OIDC认证跳转URL",
                "summary": "获取认证URL",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "SSO名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "302": {
                        "description": "Found",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/auth/sso/config": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取所有已启用的SSO配置项",
                "summary": "获取SSO配置列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/LimitRange/create": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "创建限制范围",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "限制范围配置",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/configmap/create": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "创建ConfigMap",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "请求体，包含metadata和data字段",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/configmap/ns/{ns}/name/{name}/import": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "导入文件到ConfigMap",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "ConfigMap名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "文件名",
                        "name": "fileName",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "file",
                        "description": "上传文件",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/configmap/ns/{ns}/name/{name}/{key}/update_configmap": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "更新ConfigMap中的文件内容",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "ConfigMap名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "文件名",
                        "name": "key",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "请求体，包含update_configmap字段",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/crd/group/option_list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取CRD组选项列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/crd/kind/option_list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取指定组的CRD类型选项列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "CRD组名称",
                        "name": "spec[group]",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/crd/status": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取CRD状态信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/cronjob/batch/pause": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "批量暂停 CronJob",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "批量暂停请求体，包含 name_list 和 ns_list",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/cronjob/batch/resume": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "批量恢复 CronJob",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "批量恢复请求体，包含 name_list 和 ns_list",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/cronjob/pause/ns/{ns}/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "暂停 CronJob",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "CronJob 名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/cronjob/resume/ns/{ns}/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "恢复 CronJob",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "CronJob 名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/daemonset/batch/restart": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "批量重启DaemonSet",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "包含name_list和ns_list的请求体",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/daemonset/batch/restore": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "批量恢复DaemonSet",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "包含name_list和ns_list的请求体",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/daemonset/batch/stop": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "批量停止DaemonSet",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "包含name_list和ns_list的请求体",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/daemonset/ns/{ns}/name/{name}/restart": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "重启DaemonSet",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "DaemonSet名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/daemonset/ns/{ns}/name/{name}/revision/{revision}/rollout/undo": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "回滚DaemonSet到指定版本",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "DaemonSet名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "回滚版本",
                        "name": "revision",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/daemonset/ns/{ns}/name/{name}/rollout/history": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取DaemonSet回滚历史",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "DaemonSet名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/deploy/batch/restart": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "批量重启Deployment",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Deployment名称列表",
                        "name": "name_list",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    },
                    {
                        "description": "命名空间列表",
                        "name": "ns_list",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/deploy/batch/restore": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "批量恢复Deployment",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Deployment名称列表",
                        "name": "name_list",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    },
                    {
                        "description": "命名空间列表",
                        "name": "ns_list",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/deploy/batch/stop": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "批量停止Deployment",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Deployment名称列表",
                        "name": "name_list",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    },
                    {
                        "description": "命名空间列表",
                        "name": "ns_list",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/deploy/create": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "创建Deployment",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Deployment配置",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/deploy/ns/{ns}/name/{name}/events/all": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Deployment相关事件",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Deployment名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/deploy/ns/{ns}/name/{name}/hpa": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Deployment的HPA信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Deployment名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/deploy/ns/{ns}/name/{name}/restart": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "重启单个Deployment",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Deployment名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/deploy/ns/{ns}/name/{name}/revision/{revision}/rollout/history": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Deployment版本差异",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Deployment名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "版本号",
                        "name": "revision",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/deploy/ns/{ns}/name/{name}/revision/{revision}/rollout/undo": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "回滚Deployment到指定版本",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Deployment名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "版本号",
                        "name": "revision",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/deploy/ns/{ns}/name/{name}/rollout/history": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Deployment历史版本",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Deployment名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/deploy/ns/{ns}/name/{name}/rollout/pause": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "暂停Deployment滚动更新",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Deployment名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/deploy/ns/{ns}/name/{name}/rollout/resume": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "恢复Deployment滚动更新",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Deployment名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/deploy/ns/{ns}/name/{name}/scale/replica/{replica}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "扩缩容Deployment",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Deployment名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "副本数",
                        "name": "replica",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/doc/detail": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取文档详情(含翻译)",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "请求体，包含description字段",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/doc.DetailReq"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/doc.DetailReq"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/doc/kind/{kind}/group/{group}/version/{version}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Kubernetes资源文档信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/file/delete": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "删除文件",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "文件信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pod.info"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/file/download": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "下载文件",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Pod名称",
                        "name": "podName",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "文件路径",
                        "name": "path",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "容器名称",
                        "name": "containerName",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "namespace",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/file/list": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取文件列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "文件信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pod.info"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/file/save": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "保存文件",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "文件信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pod.info"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/file/show": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "查看文件内容",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "文件信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/pod.info"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/file/upload": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "上传文件",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "容器名称",
                        "name": "containerName",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "namespace",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Pod名称",
                        "name": "podName",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "文件路径",
                        "name": "path",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "文件名",
                        "name": "fileName",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "file",
                        "description": "上传文件",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/gateway_class/option_list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取GatewayClass选项列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/helm/release/batch/uninstall": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "批量卸载Helm Release",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "批量卸载参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/helm/release/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Release列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/helm/release/ns/{ns}/name/{name}/history/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Release的历史版本",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Release名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/helm/release/ns/{ns}/name/{name}/revision/{revision}/notes": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取ReleaseNote",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Release名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "版本号",
                        "name": "revision",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/helm/release/ns/{ns}/name/{name}/revision/{revision}/values": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取安装yaml",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Release名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "版本号",
                        "name": "revision",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/helm/release/ns/{ns}/name/{name}/uninstall": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "卸载Helm Release",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Release名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/helm/release/upgrade": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "升级Helm Release",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "升级参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/helm/release/{release}/repo/{repo}/chart/{chart}/version/{version}/install": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "安装Helm Release",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Release名称",
                        "name": "release",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "仓库名称",
                        "name": "repo",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Chart名称",
                        "name": "chart",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "版本号",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "安装参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/ingress_class/option_list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取 IngressClass 选项列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/ingress_class/set_default/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "设置默认的 IngressClass",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "IngressClass 名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/k8s_gpt/cluster/{user_cluster}/result": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取集群K8sGPT分析结果",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "用户集群标识",
                        "name": "user_cluster",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/k8s_gpt/cluster/{user_cluster}/run": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "对整个集群运行K8sGPT分析",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "用户集群标识",
                        "name": "user_cluster",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/k8s_gpt/kind/{kind}/run": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "对指定资源类型运行K8sGPT分析",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/k8s_gpt/var": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取K8s资源字段信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/node/add_taints/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "添加污点",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "节点名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "污点信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/node.TaintInfo"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/node/batch/cordon": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "批量将指定的 Kubernetes 节点设置为不可调度（cordon）",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "节点名称列表",
                        "name": "name_list",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/node/batch/drain": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "批量驱逐指定的 Kubernetes 节点",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "节点名称列表",
                        "name": "name_list",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/node/batch/uncordon": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "批量解除指定节点的隔离状态（Uncordon），使其重新可调度",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "节点名称列表",
                        "name": "name_list",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/node/cordon/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "隔离指定节点",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "节点名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/node/delete_taints/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "删除污点",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "节点名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "污点信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/node.TaintInfo"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/node/drain/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "驱逐指定节点",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "节点名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/node/labels/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取所有节点上的标签",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/node/labels/unique_labels": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取选定集群中所有唯一的节点标签键，并以选项列表形式返回",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/node/list_taints/name/{name}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取某个节点上的污点",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "节点名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/node/name/option_list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取节点名称选项列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/node/name/{node_name}/cluster_id/{cluster_id}/create_kubectl_shell": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "创建kubectl shell",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "节点名称",
                        "name": "node_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "集群ID，base64编码",
                        "name": "cluster_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/node/name/{node_name}/create_node_shell": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "创建节点shell",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "节点名称",
                        "name": "node_name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/node/taints/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取所有节点上的污点",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/node/top/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "返回所有节点的资源使用率（top指标），包括CPU和内存的用量及其数值化表示，便于前端排序和展示",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/node/uncordon/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "解除指定节点隔离",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "节点名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/node/update_taints/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "修改污点",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "节点名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "污点信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/node.TaintInfo"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/node/usage/name/{name}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取节点资源使用情况",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "节点名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/ns/create_resource_quota": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "创建资源配额",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "资源配额配置",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/ns/option_list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取命名空间选项列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/pod/labels/unique_labels": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Pod唯一标签键列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/pod/logs/download/ns/{ns}/pod_name/{pod_name}/container/{container_name}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "下载Pod日志",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Pod名称",
                        "name": "pod_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "容器名称",
                        "name": "container_name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "日志文件",
                        "schema": {
                            "type": "file"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/pod/logs/sse/ns/{ns}/pod_name/{pod_name}/container/{container_name}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "流式获取Pod日志",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Pod名称",
                        "name": "pod_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "容器名称",
                        "name": "container_name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "日志流",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/pod/port_forward/ns/{ns}/name/{name}/container/{container_name}/pod_port/{pod_port}/local_port/{local_port}/start": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "开始端口转发",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Pod名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "容器名称",
                        "name": "container_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Pod端口",
                        "name": "pod_port",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "本地端口",
                        "name": "local_port",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/pod/port_forward/ns/{ns}/name/{name}/container/{container_name}/pod_port/{pod_port}/stop": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "停止端口转发",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Pod名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "容器名称",
                        "name": "container_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Pod端口",
                        "name": "pod_port",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/pod/port_forward/ns/{ns}/name/{name}/port/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "列出端口转发信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Pod名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/pod/top/ns/{ns}/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Pod资源使用情况列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间，多个用逗号分隔",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/pod/usage/ns/{ns}/name/{name}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Pod资源使用情况",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Pod名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/pod/xterm/ns/{ns}/pod_name/{pod_name}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "提供Pod容器的交互式终端会话",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Pod名称",
                        "name": "pod_name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "容器名称，默认为第一个容器",
                        "name": "container_name",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "会话结束后是否删除Pod",
                        "name": "remove",
                        "in": "query"
                    }
                ],
                "responses": {
                    "101": {
                        "description": "WebSocket连接成功",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/replicaset/batch/restart": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "批量重启ReplicaSet",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "包含name_list和ns_list的请求体",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/replicaset/batch/restore": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "批量恢复ReplicaSet",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "包含name_list和ns_list的请求体",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/replicaset/batch/stop": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "批量停止ReplicaSet",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "包含name_list和ns_list的请求体",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/replicaset/ns/{ns}/name/{name}/events/all": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取ReplicaSet相关事件列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "ReplicaSet名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/replicaset/ns/{ns}/name/{name}/hpa": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取ReplicaSet相关HPA列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "ReplicaSet名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/replicaset/ns/{ns}/name/{name}/restart": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "重启指定的ReplicaSet",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "ReplicaSet名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/service/create": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "创建Service",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "Service创建参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/statefulset/batch/restart": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "批量重启StatefulSet",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "StatefulSet名称列表",
                        "name": "name_list",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    },
                    {
                        "description": "命名空间列表",
                        "name": "ns_list",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/statefulset/batch/restore": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "批量恢复StatefulSet",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "StatefulSet名称列表",
                        "name": "name_list",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    },
                    {
                        "description": "命名空间列表",
                        "name": "ns_list",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/statefulset/batch/stop": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "批量停止StatefulSet",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "StatefulSet名称列表",
                        "name": "name_list",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    },
                    {
                        "description": "命名空间列表",
                        "name": "ns_list",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/statefulset/ns/{ns}/name/{name}/hpa": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取StatefulSet的HPA列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "StatefulSet名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/statefulset/ns/{ns}/name/{name}/restart": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "重启StatefulSet",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "StatefulSet名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/statefulset/ns/{ns}/name/{name}/revision/{revision}/rollout/undo": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "回滚StatefulSet到指定版本",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "StatefulSet名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "版本号",
                        "name": "revision",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/statefulset/ns/{ns}/name/{name}/rollout/history": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取StatefulSet滚动历史",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "StatefulSet名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/statefulset/ns/{ns}/name/{name}/scale/replica/{replica}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "扩缩容StatefulSet",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "StatefulSet名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "副本数",
                        "name": "replica",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/status/resource_count/cache_seconds/{cache}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取集群资源数量统计",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "缓存时间（秒）",
                        "name": "cache",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/storage_class/option_list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取存储类选项列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/storage_class/set_default/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "设置默认存储类",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "存储类名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/yaml/apply": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "应用YAML配置",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "YAML配置请求",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dynamic.yamlRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/yaml/delete": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "删除YAML配置",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "description": "YAML配置请求",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dynamic.yamlRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/yaml/upload": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "上传YAML文件并应用",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "file",
                        "description": "YAML文件",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/add_node_affinity/ns/{ns}/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "添加节点亲和性配置",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "节点亲和性配置",
                        "name": "nodeAffinity",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dynamic.nodeAffinity"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/add_pod_affinity/ns/{ns}/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "添加Pod亲和性配置",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Pod亲和性配置",
                        "name": "podAffinity",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dynamic.podAffinity"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/add_pod_anti_affinity/ns/{ns}/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "添加Pod反亲和性",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Pod反亲和性配置",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dynamic.podAffinity"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/add_tolerations/ns/{ns}/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "添加资源容忍度",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "容忍度配置信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dynamic.Tolerations"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/annotations/ns/{ns}/name/{name}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "列出资源注解",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/batch/remove": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "批量删除资源",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "资源名称列表",
                        "name": "name_list",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    },
                    {
                        "description": "命名空间列表",
                        "name": "ns_list",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/container_env/ns/{ns}/name/{name}/container/{container_name}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取容器环境变量信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "容器名称",
                        "name": "container_name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/container_health_checks/ns/{ns}/name/{name}/container/{container_name}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取容器健康检查信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "容器名称",
                        "name": "container_name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/container_info/ns/{ns}/name/{name}/container/{container_name}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取容器基本信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "容器名称",
                        "name": "container_name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/container_resources_info/ns/{ns}/name/{name}/container/{container_name}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取容器资源信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "容器名称",
                        "name": "container_name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/delete_node_affinity/ns/{ns}/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "删除节点亲和性配置",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "节点亲和性配置",
                        "name": "nodeAffinity",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dynamic.nodeAffinity"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/delete_pod_affinity/ns/{ns}/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "删除Pod亲和性配置",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Pod亲和性配置",
                        "name": "podAffinity",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dynamic.podAffinity"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/delete_pod_anti_affinity/ns/{ns}/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "删除Pod反亲和性",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Pod反亲和性配置",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dynamic.podAffinity"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/delete_tolerations/ns/{ns}/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "删除资源容忍度",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "容忍度配置信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dynamic.Tolerations"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/describe/ns/{ns}/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "描述资源",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/force_remove": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "批量强制删除资源",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "资源名称列表",
                        "name": "name_list",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    },
                    {
                        "description": "命名空间列表",
                        "name": "ns_list",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/image_pull_secrets/ns/{ns}/name/{name}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取容器镜像拉取密钥选项",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/list/ns/{ns}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取资源列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/list_node_affinity/ns/{ns}/name/{name}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取节点亲和性配置",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "object"
                            }
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/list_pod_affinity/ns/{ns}/name/{name}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Pod亲和性配置",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "object"
                            }
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/list_pod_anti_affinity/ns/{ns}/name/{name}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Pod反亲和性列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/list_tolerations/ns/{ns}/name/{name}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取资源容忍度列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/ns/{ns}/name/{name}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取资源YAML",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/ns/{ns}/name/{name}/event": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取资源事件",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/ns/{ns}/name/{name}/hpa": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取资源HPA信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/ns/{ns}/name/{name}/json": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取资源JSON",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/ns/{ns}/name/{name}/links/configmap": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Pod关联的ConfigMap",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/ns/{ns}/name/{name}/links/endpoints": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Pod关联的端点",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/ns/{ns}/name/{name}/links/env": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Pod关联的环境变量",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/ns/{ns}/name/{name}/links/envFromPod": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Pod关联的来自其他Pod的环境变量",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/ns/{ns}/name/{name}/links/ingress": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Pod关联的Ingress",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/ns/{ns}/name/{name}/links/node": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Pod关联的节点",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/ns/{ns}/name/{name}/links/pv": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Pod关联的PV",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/ns/{ns}/name/{name}/links/pvc": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Pod关联的PVC",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/ns/{ns}/name/{name}/links/secret": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Pod关联的Secret",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/ns/{ns}/name/{name}/links/services": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "获取Pod关联的服务",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/ns/{ns}/name/{name}/scale/replica/{replica}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "扩缩容资源",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "副本数",
                        "name": "replica",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/remove/ns/{ns}/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "删除单个资源",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/update/ns/{ns}/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "更新资源",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "资源YAML内容",
                        "name": "yaml",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/update_annotations/ns/{ns}/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "更新资源注解",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "注解键值对",
                        "name": "annotations",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/update_env/ns/{ns}/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "更新容器环境变量",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "容器环境变量信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dynamic.ContainerEnv"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/update_health_checks/ns/{ns}/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "更新容器健康检查配置",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "健康检查配置信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dynamic.HealthCheckInfo"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/update_image/ns/{ns}/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "更新容器镜像标签",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "镜像信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dynamic.imageInfo"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/update_labels/ns/{ns}/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "更新资源标签",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "标签键值对",
                        "name": "labels",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/update_node_affinity/ns/{ns}/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "更新节点亲和性配置",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "节点亲和性配置",
                        "name": "nodeAffinity",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dynamic.nodeAffinity"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/update_pod_affinity/ns/{ns}/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "更新Pod亲和性配置",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Pod亲和性配置",
                        "name": "podAffinity",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dynamic.podAffinity"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/update_pod_anti_affinity/ns/{ns}/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "更新Pod反亲和性",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Pod反亲和性配置",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dynamic.podAffinity"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/update_resources/ns/{ns}/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "更新容器资源配置",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "资源配置信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dynamic.resourceInfo"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/k8s/cluster/{cluster}/{kind}/group/{group}/version/{version}/update_tolerations/ns/{ns}/name/{name}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "summary": "更新资源容忍度",
                "parameters": [
                    {
                        "type": "string",
                        "description": "集群名称",
                        "name": "cluster",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源类型",
                        "name": "kind",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API组",
                        "name": "group",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "API版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "命名空间",
                        "name": "ns",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "资源名称",
                        "name": "name",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "容忍度配置信息",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/dynamic.Tolerations"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/mgm/cluster/{cluster}/reconnect": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "重新连接一个已断开的集群",
                "summary": "重新连接集群",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Base64编码的集群ID",
                        "name": "cluster",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "已执行，请稍后刷新",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/mgm/custom/template/delete/{ids}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "删除一个或多个自定义模板",
                "summary": "删除模板",
                "parameters": [
                    {
                        "type": "string",
                        "description": "要删除的模板ID，多个用逗号分隔",
                        "name": "ids",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "操作成功",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/mgm/custom/template/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取所有自定义模板信息",
                "summary": "模板列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/mgm/custom/template/save": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "新增或更新自定义模板",
                "summary": "保存模板",
                "parameters": [
                    {
                        "description": "模板信息",
                        "name": "template",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.CustomTemplate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "返回模板ID",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/mgm/helm/chart/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取所有Helm Chart信息",
                "summary": "Helm Chart列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/mgm/helm/repo/{repo}/chart/{chart}/version/{version}/values": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取指定Helm仓库、Chart及版本的默认values.yaml内容",
                "summary": "获取Chart的默认values.yaml",
                "parameters": [
                    {
                        "type": "string",
                        "description": "仓库名称",
                        "name": "repo",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Chart名称",
                        "name": "chart",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Chart版本",
                        "name": "version",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "yaml内容",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/mgm/helm/repo/{repo}/chart/{chart}/versions": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取指定Helm仓库和Chart的所有版本列表",
                "summary": "Chart版本列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "仓库名称",
                        "name": "repo",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Chart名称",
                        "name": "chart",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/mgm/log/operation/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取所有操作日志",
                "summary": "操作日志列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/mgm/log/shell/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取所有Shell操作日志",
                "summary": "Shell日志列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/mgm/user/profile": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前登录用户的详细信息",
                "summary": "获取用户信息",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/mgm/user/profile/2fa/disable": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "禁用当前用户的二步验证",
                "summary": "禁用2FA",
                "responses": {
                    "200": {
                        "description": "操作成功",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/mgm/user/profile/2fa/enable": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "验证并启用当前用户的二步验证",
                "summary": "启用2FA",
                "parameters": [
                    {
                        "description": "验证码",
                        "name": "code",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    },
                    {
                        "description": "应用名称",
                        "name": "app_name",
                        "in": "body",
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "操作成功",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/mgm/user/profile/2fa/generate": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "生成当前用户的二步验证密钥和二维码",
                "summary": "生成2FA密钥",
                "responses": {
                    "200": {
                        "description": "返回密钥、二维码和备用码",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/mgm/user/profile/api_keys/create": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "为当前用户创建一个新的API密钥",
                "summary": "创建API密钥",
                "parameters": [
                    {
                        "description": "密钥描述",
                        "name": "description",
                        "in": "body",
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "操作成功",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/mgm/user/profile/api_keys/delete/{id}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "删除指定ID的API密钥",
                "summary": "删除API密钥",
                "parameters": [
                    {
                        "type": "string",
                        "description": "API密钥ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "操作成功",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/mgm/user/profile/api_keys/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前用户的所有API密钥",
                "summary": "获取API密钥列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/mgm/user/profile/cluster/permissions/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "列出当前登录用户所拥有的集群权限",
                "summary": "获取用户集群权限",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/mgm/user/profile/mcp_keys/create": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "为当前用户创建一个新的MCP密钥（10年有效期）",
                "summary": "创建MCP密钥",
                "parameters": [
                    {
                        "description": "密钥描述",
                        "name": "description",
                        "in": "body",
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "操作成功",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/mgm/user/profile/mcp_keys/delete/{id}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "删除指定ID的MCP密钥",
                "summary": "删除MCP密钥",
                "parameters": [
                    {
                        "type": "string",
                        "description": "MCP密钥ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "操作成功",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/mgm/user/profile/mcp_keys/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前用户的所有MCP密钥",
                "summary": "获取MCP密钥列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/mgm/user/profile/update_psw": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "修改当前登录用户的密码",
                "summary": "修改密码",
                "parameters": [
                    {
                        "description": "新密码（加密后）",
                        "name": "password",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "string"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "操作成功",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/params/cluster/all": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前登录用户可见的集群详细信息（表格）",
                "summary": "集群表格列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/params/cluster/option_list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前登录用户可选的集群列表（下拉选项）",
                "summary": "集群选项列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/params/condition/reverse/list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取所有启用的翻转显示指标名称",
                "summary": "翻转指标列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/params/config/{key}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取指定key的系统配置项",
                "summary": "获取配置项",
                "parameters": [
                    {
                        "type": "string",
                        "description": "配置项key",
                        "name": "key",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/params/helm/repo/option_list": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取所有Helm仓库名称，用于下拉选项",
                "summary": "Helm仓库选项列表",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/params/user/role": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前登录用户的角色及默认集群",
                "summary": "获取用户角色信息",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/params/version": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前软件的版本及构建信息",
                "summary": "获取版本信息",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "doc.DetailReq": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "translate": {
                    "type": "string"
                }
            }
        },
        "dynamic.ContainerEnv": {
            "type": "object",
            "properties": {
                "container_name": {
                    "type": "string"
                },
                "envs": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                }
            }
        },
        "dynamic.HealthCheckInfo": {
            "type": "object",
            "properties": {
                "container_name": {
                    "type": "string"
                },
                "liveness_probe": {
                    "type": "object",
                    "additionalProperties": true
                },
                "liveness_type": {
                    "type": "string"
                },
                "readiness_probe": {
                    "type": "object",
                    "additionalProperties": true
                },
                "readiness_type": {
                    "type": "string"
                }
            }
        },
        "dynamic.Tolerations": {
            "type": "object",
            "properties": {
                "effect": {
                    "type": "string"
                },
                "key": {
                    "type": "string"
                },
                "operator": {
                    "type": "string"
                },
                "tolerationSeconds": {
                    "type": "integer"
                },
                "value": {
                    "type": "string"
                }
            }
        },
        "dynamic.imageInfo": {
            "type": "object",
            "properties": {
                "container_name": {
                    "type": "string"
                },
                "image": {
                    "type": "string"
                },
                "image_pull_policy": {
                    "type": "string"
                },
                "image_pull_secrets": {
                    "type": "string"
                },
                "tag": {
                    "type": "string"
                }
            }
        },
        "dynamic.nodeAffinity": {
            "type": "object",
            "properties": {
                "key": {
                    "type": "string"
                },
                "operator": {
                    "type": "string"
                },
                "values": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "dynamic.podAffinity": {
            "type": "object",
            "properties": {
                "labelSelector": {
                    "type": "object",
                    "properties": {
                        "matchLabels": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                },
                "topologyKey": {
                    "type": "string"
                }
            }
        },
        "dynamic.resourceInfo": {
            "type": "object",
            "properties": {
                "container_name": {
                    "type": "string"
                },
                "limit_cpu": {
                    "type": "string"
                },
                "limit_memory": {
                    "type": "string"
                },
                "request_cpu": {
                    "type": "string"
                },
                "request_memory": {
                    "type": "string"
                }
            }
        },
        "dynamic.yamlRequest": {
            "type": "object",
            "required": [
                "yaml"
            ],
            "properties": {
                "yaml": {
                    "type": "string"
                }
            }
        },
        "models.Config": {
            "type": "object",
            "properties": {
                "any_select": {
                    "type": "boolean"
                },
                "created_at": {
                    "description": "Automatically managed by GORM for creation time",
                    "type": "string"
                },
                "enable_ai": {
                    "description": "是否启用AI功能，默认开启",
                    "type": "boolean"
                },
                "id": {
                    "type": "integer"
                },
                "image_pull_timeout": {
                    "description": "镜像拉取超时时间（秒）",
                    "type": "integer"
                },
                "jwt_token_secret": {
                    "type": "string"
                },
                "kubectl_shell_image": {
                    "type": "string"
                },
                "login_type": {
                    "type": "string"
                },
                "max_history": {
                    "description": "模型对话上下文历史记录数",
                    "type": "integer"
                },
                "max_iterations": {
                    "description": "模型自动对话的最大轮数",
                    "type": "integer"
                },
                "model_id": {
                    "type": "integer"
                },
                "node_shell_image": {
                    "type": "string"
                },
                "print_config": {
                    "type": "boolean"
                },
                "product_name": {
                    "description": "产品名称",
                    "type": "string"
                },
                "resource_cache_timeout": {
                    "description": "资源缓存时间（秒）",
                    "type": "integer"
                },
                "updated_at": {
                    "description": "Automatically managed by GORM for update time",
                    "type": "string"
                },
                "use_built_in_model": {
                    "type": "boolean"
                }
            }
        },
        "models.CustomTemplate": {
            "type": "object",
            "properties": {
                "cluster": {
                    "description": "模板类型，最大长度 100",
                    "type": "string"
                },
                "content": {
                    "description": "模板内容，支持大文本存储",
                    "type": "string"
                },
                "created_at": {
                    "description": "Automatically managed by GORM for creation time",
                    "type": "string"
                },
                "created_by": {
                    "description": "创建者",
                    "type": "string"
                },
                "id": {
                    "description": "模板 ID，主键，自增",
                    "type": "integer"
                },
                "is_global": {
                    "description": "模板类型，最大长度 100",
                    "type": "boolean"
                },
                "kind": {
                    "description": "模板类型，最大长度 100",
                    "type": "string"
                },
                "name": {
                    "description": "模板名称，非空，最大长度 255",
                    "type": "string"
                },
                "updated_at": {
                    "description": "Automatically managed by GORM for update time",
                    "type": "string"
                }
            }
        },
        "models.HelmRepository": {
            "type": "object",
            "properties": {
                "auth_type": {
                    "type": "string"
                },
                "caFile": {
                    "type": "string"
                },
                "certFile": {
                    "type": "string"
                },
                "created_at": {
                    "description": "Automatically managed by GORM for creation time",
                    "type": "string"
                },
                "description": {
                    "description": "仓库描述",
                    "type": "string"
                },
                "encrypted_secret": {
                    "type": "string"
                },
                "generated": {
                    "description": "repo 索引文件创建时间",
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "insecure_skip_tls_verify": {
                    "type": "boolean"
                },
                "is_active": {
                    "description": "是否启用",
                    "type": "boolean"
                },
                "keyFile": {
                    "type": "string"
                },
                "name": {
                    "description": "仓库名称（唯一）",
                    "type": "string"
                },
                "pass_credentials_all": {
                    "type": "boolean"
                },
                "password": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                },
                "updated_at": {
                    "description": "Automatically managed by GORM for update time",
                    "type": "string"
                },
                "url": {
                    "description": "仓库地址（如 https://charts.example.com）",
                    "type": "string"
                },
                "username": {
                    "description": "认证用户名（加密存储）",
                    "type": "string"
                }
            }
        },
        "models.MCPServerConfig": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "enabled": {
                    "type": "boolean"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "url": {
                    "type": "string"
                }
            }
        },
        "models.User": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "disabled": {
                    "description": "是否启用",
                    "type": "boolean"
                },
                "group_names": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "password": {
                    "type": "string"
                },
                "salt": {
                    "type": "string"
                },
                "source": {
                    "description": "来源，如：db, ldap, oauth",
                    "type": "string"
                },
                "two_fa_app_name": {
                    "description": "2FA应用名称，用于提醒用户使用的是哪个软件",
                    "type": "string"
                },
                "two_fa_backup_codes": {
                    "description": "备用恢复码，逗号分隔",
                    "type": "string"
                },
                "two_fa_enabled": {
                    "description": "是否启用2FA",
                    "type": "boolean"
                },
                "two_fa_secret": {
                    "description": "2FA密钥",
                    "type": "string"
                },
                "two_fa_type": {
                    "description": "2FA类型：如 'totp', 'sms', 'email'",
                    "type": "string"
                },
                "updated_at": {
                    "description": "Automatically managed by GORM for update time",
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "models.UserGroup": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "group_name": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "role": {
                    "description": "管理员/只读",
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "node.TaintInfo": {
            "type": "object",
            "properties": {
                "effect": {
                    "type": "string"
                },
                "key": {
                    "type": "string"
                },
                "value": {
                    "type": "string"
                }
            }
        },
        "pod.info": {
            "type": "object",
            "properties": {
                "containerName": {
                    "type": "string"
                },
                "fileContext": {
                    "type": "string"
                },
                "fileName": {
                    "type": "string"
                },
                "isDir": {
                    "type": "boolean"
                },
                "namespace": {
                    "type": "string"
                },
                "path": {
                    "type": "string"
                },
                "podName": {
                    "type": "string"
                },
                "size": {
                    "type": "integer"
                },
                "type": {
                    "description": "只有file类型可以查、下载",
                    "type": "string"
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

type swaggerInfo struct {
	Version     string
	Host        string
	BasePath    string
	Schemes     []string
	Title       string
	Description string
}

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = swaggerInfo{
	Version:     "1.0",
	Host:        "",
	BasePath:    "",
	Schemes:     []string{},
	Title:       "k8m API",
	Description: "请输入以 `Bearer ` 开头的 Token，例：Bearer xxxxxxxx。未列出接口请参考前端调用方法。Token在个人中心-API密钥菜单下申请。",
}

type s struct{}

func (s *s) ReadDoc() string {
	sInfo := SwaggerInfo
	sInfo.Description = strings.Replace(sInfo.Description, "\n", "\\n", -1)

	t, err := template.New("swagger_info").Funcs(template.FuncMap{
		"marshal": func(v interface{}) string {
			a, _ := json.Marshal(v)
			return string(a)
		},
	}).Parse(doc)
	if err != nil {
		return doc
	}

	var tpl bytes.Buffer
	if err := t.Execute(&tpl, sInfo); err != nil {
		return doc
	}

	return tpl.String()
}

func init() {
	swag.Register(swag.Name, &s{})
}
